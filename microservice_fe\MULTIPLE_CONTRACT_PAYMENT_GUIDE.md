# Hướng dẫn sử dụng tính năng Thanh toán nhiều hợp đồng

## Tổng quan

Tính năng mới cho phép:
- **M<PERSON>t hợp đồng có thể có nhiều thanh toán** (thanh toán từng phần)
- **Một thanh toán có thể cho nhiều hợp đồng** (thanh to<PERSON> gộ<PERSON>)
- **<PERSON><PERSON> lịch sử thanh toán chi tiết** cho từng hợp đồng

## Cách sử dụng

### 1. <PERSON>h toán nhiều hợp đồng

#### Bước 1: Chọ<PERSON> khách hàng
- V<PERSON>o trang **Thanh toán khách hàng**
- Tì<PERSON> kiếm và chọn khách hàng có nhiều hợp đồng cần thanh toán

#### Bước 2: Khởi tạo thanh toán nhiều hợp đồng
- <PERSON>rong danh sách hợp đồng, nhấn nút **"<PERSON><PERSON> toán nhiều hợp đồng"**
- <PERSON><PERSON><PERSON> này chỉ hiển thị khi khách hàng có từ 2 hợp đồng trở lên và có hợp đồng chưa thanh toán hết

#### Bước 3: Cấu hình thanh toán
- **Phương thức thanh toán**: Chọn từ dropdown (Tiền mặt, Chuyển khoản, v.v.)
- **Ghi chú**: Nhập ghi chú cho thanh toán (tùy chọn)
- **Danh sách hợp đồng**: Hệ thống tự động hiển thị tất cả hợp đồng với số tiền còn lại

#### Bước 4: Điều chỉnh số tiền
- Mỗi hợp đồng có trường nhập số tiền thanh toán
- Số tiền mặc định là số tiền còn lại của hợp đồng
- Có thể điều chỉnh số tiền nhỏ hơn hoặc bằng số tiền còn lại
- Có thể xóa hợp đồng khỏi danh sách thanh toán bằng nút **"Xóa"**

#### Bước 5: Thêm/bớt hợp đồng
- Phần **"Thêm hợp đồng khác"** hiển thị các hợp đồng chưa được chọn
- Nhấn vào chip hợp đồng để thêm vào danh sách thanh toán

#### Bước 6: Xác nhận thanh toán
- Kiểm tra **tổng số tiền thanh toán** ở cuối form
- Nhấn nút **"Thanh toán [số tiền]"** để xác nhận
- Hệ thống sẽ validate và tạo thanh toán

### 2. Thanh toán đơn lẻ (như cũ)

- Nhấn nút **"Thanh toán"** trên từng hợp đồng riêng lẻ
- Tính năng này vẫn hoạt động bình thường để tương thích ngược

### 3. Xem lịch sử thanh toán

#### Cách truy cập:
- Trong danh sách hợp đồng, nhấn nút **"Lịch sử"** bên cạnh nút "Thanh toán"

#### Thông tin hiển thị:
- **Thanh toán đơn lẻ**: Các thanh toán được tạo theo cách cũ (one-to-many)
- **Thanh toán nhiều hợp đồng**: Các phân bổ thanh toán từ tính năng mới (many-to-many)
- **Tổng số tiền đã thanh toán**: Tổng cộng từ cả hai loại thanh toán

## Validation và Kiểm tra

### Khi tạo thanh toán nhiều hợp đồng:

1. **Tổng số tiền phân bổ phải bằng tổng số tiền thanh toán**
2. **Mỗi hợp đồng phải tồn tại và đang ở trạng thái ACTIVE hoặc PENDING**
3. **Số tiền phân bổ cho mỗi hợp đồng không được vượt quá số tiền còn lại**
4. **Phải có ít nhất một hợp đồng được chọn**
5. **Số tiền thanh toán phải lớn hơn 0**

### Thông báo lỗi thường gặp:

- *"Tổng số tiền phân bổ phải bằng tổng số tiền thanh toán"*
  → Điều chỉnh số tiền các hợp đồng cho khớp

- *"Số tiền thanh toán cho hợp đồng ID X không được vượt quá số tiền còn lại"*
  → Giảm số tiền thanh toán cho hợp đồng đó

- *"Phải có ít nhất một hợp đồng để thanh toán"*
  → Thêm ít nhất một hợp đồng vào danh sách

## Ưu điểm của tính năng mới

### 1. Linh hoạt trong thanh toán:
- Khách hàng có thể thanh toán từng phần cho hợp đồng lớn
- Có thể thanh toán gộp nhiều hợp đồng trong một lần

### 2. Quản lý tốt hơn:
- Theo dõi chi tiết từng khoản thanh toán
- Lịch sử thanh toán rõ ràng, đầy đủ
- Tính toán số tiền còn lại chính xác

### 3. Tương thích ngược:
- Hệ thống cũ vẫn hoạt động bình thường
- Dữ liệu cũ được bảo toàn và hiển thị đầy đủ

### 4. Giao diện thân thiện:
- Form thanh toán trực quan, dễ sử dụng
- Validation real-time
- Thông báo lỗi rõ ràng

## Lưu ý kỹ thuật

- Backend API mới: `/api/customer-payment/multiple-contracts`
- Tương thích với API cũ: `/api/customer-payment`
- Database: Bảng mới `contract_payments` để lưu mối quan hệ many-to-many
- Frontend: Component mới `MultipleContractPaymentForm` và `PaymentHistoryDialog`
