import { CustomerPayment, Customer, CustomerContract, ContractPayment, CreatePaymentRequest } from '../../models';
import { get, post } from '../api/apiClient';

const BASE_URL = '/api/customer-payment';

export const customerPaymentService = {
  // L<PERSON>y tất cả các thanh toán
  getAllPayments: async (): Promise<CustomerPayment[]> => {
    return get<CustomerPayment[]>(BASE_URL);
  },

  // Lấy thanh toán theo ID
  getPaymentById: async (id: number): Promise<CustomerPayment> => {
    return get<CustomerPayment>(`${BASE_URL}/payment/${id}`);
  },

  // Tạo thanh toán mới
  createPayment: async (payment: CustomerPayment): Promise<CustomerPayment> => {
    console.log('🚀 Payment service: Creating payment...', {
      contractId: payment.customerContractId,
      customerId: payment.customerId,
      amount: payment.paymentAmount,
      method: payment.paymentMethod,
      paymentDate: payment.paymentDate
    });

    const result = await post<CustomerPayment>(BASE_URL, payment);

    console.log('✅ Payment service: Payment created successfully:', {
      id: result.id,
      amount: result.paymentAmount,
      contractId: result.customerContractId
    });

    return result;
  },

  // Tìm kiếm khách hàng
  searchCustomers: async (fullName?: string, phoneNumber?: string): Promise<Customer[]> => {
    let url = `${BASE_URL}/customer/search`;
    const params = [];
    if (fullName) params.push(`fullName=${encodeURIComponent(fullName)}`);
    if (phoneNumber) params.push(`phoneNumber=${encodeURIComponent(phoneNumber)}`);
    if (params.length > 0) url += `?${params.join('&')}`;
    return get<Customer[]>(url);
  },

  // Lấy thanh toán theo khách hàng
  getPaymentsByCustomerId: async (customerId: number): Promise<CustomerPayment[]> => {
    return get<CustomerPayment[]>(`${BASE_URL}/customer/${customerId}`);
  },

  // Lấy thanh toán theo hợp đồng
  getPaymentsByContractId: async (contractId: number): Promise<CustomerPayment[]> => {
    return get<CustomerPayment[]>(`${BASE_URL}/contract/${contractId}`);
  },

  // Lấy hợp đồng đang hoạt động của khách hàng
  getActiveContractsByCustomerId: async (customerId: number): Promise<CustomerContract[]> => {
    return get<CustomerContract[]>(`${BASE_URL}/customer/${customerId}/active-contracts`);
  },

  // Lấy thông tin thanh toán của hợp đồng
  getContractPaymentInfo: async (contractId: number): Promise<CustomerContract> => {
    return get<CustomerContract>(`${BASE_URL}/contract/${contractId}/payment-info`);
  },

  // Lấy tổng số tiền đã thanh toán của hợp đồng
  getTotalPaidAmountByContractId: async (contractId: number): Promise<number> => {
    return get<number>(`${BASE_URL}/contract/${contractId}/total-paid`);
  },

  // Lấy số tiền còn lại của hợp đồng
  getRemainingAmountByContractId: async (contractId: number): Promise<number> => {
    return get<number>(`${BASE_URL}/contract/${contractId}/remaining-amount`);
  },

  // API mới - Tạo thanh toán cho nhiều hợp đồng
  createPaymentWithMultipleContracts: async (request: CreatePaymentRequest): Promise<CustomerPayment> => {
    console.log('🚀 Payment service: Creating payment with multiple contracts...', {
      customerId: request.customerId,
      totalAmount: request.totalAmount,
      contractCount: request.contractPayments.length,
      contracts: request.contractPayments.map(cp => ({
        contractId: cp.contractId,
        amount: cp.allocatedAmount
      })),
      fullRequest: request
    });

    try {
      const result = await post<CustomerPayment>(`${BASE_URL}/multiple-contracts`, request);

      console.log('✅ Payment service: Payment with multiple contracts created successfully:', {
        id: result.id,
        totalAmount: result.paymentAmount,
        contractPayments: result.contractPayments?.length || 0
      });

      return result;
    } catch (error) {
      console.error('❌ Payment service: Error creating payment with multiple contracts:', error);
      throw error;
    }
  },

  // Lấy danh sách thanh toán theo payment ID
  getContractPaymentsByPaymentId: async (paymentId: number): Promise<ContractPayment[]> => {
    return get<ContractPayment[]>(`${BASE_URL}/payment/${paymentId}/contract-payments`);
  },

  // Lấy danh sách thanh toán theo contract ID
  getContractPaymentsByContractId: async (contractId: number): Promise<ContractPayment[]> => {
    return get<ContractPayment[]>(`${BASE_URL}/contract/${contractId}/contract-payments`);
  },
};
