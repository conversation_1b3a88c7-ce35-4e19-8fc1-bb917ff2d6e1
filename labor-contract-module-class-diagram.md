# Biểu đồ lớp chi tiết - <PERSON><PERSON><PERSON>ý hợp đồng thuê lao động (Microservice Architecture)

## Tổng quan kiến trúc
Module này được thiết kế theo kiến trúc microservice với các service độc lập:
- **customer-contract-service**: <PERSON><PERSON><PERSON><PERSON> lý hợp đồng khách hàng
- **customer-service**: Qu<PERSON>n lý thông tin khách hàng  
- **job-service**: Quản lý loại công việc
- **api-gateway**: <PERSON><PERSON><PERSON> tuyến và xử lý request

## Biểu đồ lớp Mermaid

```mermaid
classDiagram
    %% ===== CUSTOMER-CONTRACT-SERVICE =====
    namespace CustomerContractService {
        class CustomerContractController {
            -CustomerContractService customerContractService
            +createContract(CustomerContract): ResponseEntity~CustomerContract~
            +getContractById(Long): ResponseEntity~CustomerContract~
            +getContractsByCustomerId(Long): ResponseEntity~List~CustomerContract~~
            +updateContract(Long, CustomerContract): ResponseEntity~CustomerContract~
            +deleteContract(Long): ResponseEntity~Void~
            +getAllContracts(): ResponseEntity~List~CustomerContract~~
        }

        class CustomerContractService {
            -CustomerContractRepository customerContractRepository
            -JobDetailRepository jobDetailRepository
            -WorkShiftRepository workShiftRepository
            -CustomerServiceClient customerServiceClient
            -JobServiceClient jobServiceClient
            +createContract(CustomerContract): CustomerContract
            +getContractById(Long): CustomerContract
            +getContractsByCustomerId(Long): List~CustomerContract~
            +updateContract(Long, CustomerContract): CustomerContract
            +deleteContract(Long): void
            +getAllContracts(): List~CustomerContract~
            +calculateTotalAmount(CustomerContract): Double
            +validateContract(CustomerContract): boolean
        }

        class CustomerContract {
            -Long id
            -Long customerId
            -LocalDate startDate
            -LocalDate endDate
            -Double totalAmount
            -Double paidAmount
            -String description
            -String address
            -Integer status
            -List~JobDetail~ jobDetails
            -LocalDateTime createdAt
            -LocalDateTime updatedAt
            +calculateWorkingDays(): Integer
            +calculateTotalSalary(): Double
            +isActive(): boolean
            +isPending(): boolean
            +isCompleted(): boolean
            +isCancelled(): boolean
        }

        class JobDetail {
            -Long id
            -CustomerContract contract
            -Long jobCategoryId
            -LocalDate startDate
            -LocalDate endDate
            -String workLocation
            -List~WorkShift~ workShifts
            -LocalDateTime createdAt
            -LocalDateTime updatedAt
            +calculateJobAmount(): Double
            +getWorkingDaysCount(): Integer
        }

        class WorkShift {
            -Long id
            -JobDetail jobDetail
            -String startTime
            -String endTime
            -Integer numberOfWorkers
            -Double salary
            -String workingDays
            -LocalDateTime createdAt
            -LocalDateTime updatedAt
            +calculateShiftAmount(): Double
            +getWorkingDaysList(): List~String~
            +calculateActualWorkingDates(LocalDate, LocalDate): List~LocalDate~
        }

        class CustomerContractRepository {
            <<interface>>
            +findByCustomerId(Long): List~CustomerContract~
            +findByStatus(Integer): List~CustomerContract~
            +findByDateRange(LocalDate, LocalDate): List~CustomerContract~
        }

        class JobDetailRepository {
            <<interface>>
            +findByContractId(Long): List~JobDetail~
            +findByJobCategoryId(Long): List~JobDetail~
        }

        class WorkShiftRepository {
            <<interface>>
            +findByJobDetailId(Long): List~WorkShift~
        }
    }

    %% ===== FEIGN CLIENTS =====
    namespace FeignClients {
        class CustomerServiceClient {
            <<interface>>
            +getCustomerById(Long): Customer
            +getAllCustomers(): List~Customer~
            +searchCustomers(String): List~Customer~
        }

        class JobServiceClient {
            <<interface>>
            +getJobCategoryById(Long): JobCategory
            +getAllJobCategories(): List~JobCategory~
        }
    }

    %% ===== CUSTOMER-SERVICE =====
    namespace CustomerService {
        class CustomerController {
            -CustomerService customerService
            +getCustomerById(Long): ResponseEntity~Customer~
            +getAllCustomers(): ResponseEntity~List~Customer~~
            +searchCustomers(String): ResponseEntity~List~Customer~~
            +createCustomer(Customer): ResponseEntity~Customer~
            +updateCustomer(Long, Customer): ResponseEntity~Customer~
        }

        class CustomerService {
            -CustomerRepository customerRepository
            +getCustomerById(Long): Customer
            +getAllCustomers(): List~Customer~
            +searchCustomers(String): List~Customer~
            +createCustomer(Customer): Customer
            +updateCustomer(Long, Customer): Customer
        }

        class Customer {
            -Long id
            -String fullname
            -String companyName
            -String phoneNumber
            -String email
            -String address
            -LocalDateTime createdAt
            -LocalDateTime updatedAt
            +getDisplayName(): String
            +isValidEmail(): boolean
            +isValidPhone(): boolean
        }

        class CustomerRepository {
            <<interface>>
            +findByFullnameContaining(String): List~Customer~
            +findByPhoneNumberContaining(String): List~Customer~
            +findByCompanyNameContaining(String): List~Customer~
        }
    }

    %% ===== JOB-SERVICE =====
    namespace JobService {
        class JobCategoryController {
            -JobCategoryService jobCategoryService
            +getJobCategoryById(Long): ResponseEntity~JobCategory~
            +getAllJobCategories(): ResponseEntity~List~JobCategory~~
            +createJobCategory(JobCategory): ResponseEntity~JobCategory~
            +updateJobCategory(Long, JobCategory): ResponseEntity~JobCategory~
        }

        class JobCategoryService {
            -JobCategoryRepository jobCategoryRepository
            +getJobCategoryById(Long): JobCategory
            +getAllJobCategories(): List~JobCategory~
            +createJobCategory(JobCategory): JobCategory
            +updateJobCategory(Long, JobCategory): JobCategory
        }

        class JobCategory {
            -Long id
            -String name
            -String description
            -Boolean isDeleted
            -LocalDateTime createdAt
            -LocalDateTime updatedAt
            +isActive(): boolean
        }

        class JobCategoryRepository {
            <<interface>>
            +findByIsDeletedFalse(): List~JobCategory~
            +findByNameContaining(String): List~JobCategory~
        }
    }

    %% ===== API-GATEWAY =====
    namespace ApiGateway {
        class GatewayConfig {
            +customerContractRoutes(): RouteLocator
            +customerRoutes(): RouteLocator
            +jobRoutes(): RouteLocator
        }

        class CorsConfig {
            +corsConfigurationSource(): CorsConfigurationSource
        }
    }

    %% ===== RELATIONSHIPS =====
    CustomerContractController --> CustomerContractService
    CustomerContractService --> CustomerContractRepository
    CustomerContractService --> JobDetailRepository
    CustomerContractService --> WorkShiftRepository
    CustomerContractService --> CustomerServiceClient
    CustomerContractService --> JobServiceClient

    CustomerContract ||--o{ JobDetail : contains
    JobDetail ||--o{ WorkShift : contains

    CustomerServiceClient --> CustomerController : HTTP call
    JobServiceClient --> JobCategoryController : HTTP call

    CustomerController --> CustomerService
    CustomerService --> CustomerRepository

    JobCategoryController --> JobCategoryService
    JobCategoryService --> JobCategoryRepository

    %% ===== ENUMS =====
    class ContractStatus {
        <<enumeration>>
        PENDING = 0
        ACTIVE = 1
        COMPLETED = 2
        CANCELLED = 3
    }

    CustomerContract --> ContractStatus : uses
```

## Mô tả chi tiết các thành phần

### 1. Customer-Contract-Service
**Chức năng chính**: Quản lý hợp đồng khách hàng, chi tiết công việc và ca làm việc

**Các lớp chính**:
- `CustomerContractController`: REST API endpoints
- `CustomerContractService`: Business logic xử lý hợp đồng
- `CustomerContract`: Entity chính chứa thông tin hợp đồng
- `JobDetail`: Chi tiết công việc trong hợp đồng
- `WorkShift`: Ca làm việc cụ thể

### 2. Customer-Service
**Chức năng chính**: Quản lý thông tin khách hàng

**Các lớp chính**:
- `CustomerController`: REST API cho khách hàng
- `CustomerService`: Business logic khách hàng
- `Customer`: Entity khách hàng

### 3. Job-Service
**Chức năng chính**: Quản lý loại công việc

**Các lớp chính**:
- `JobCategoryController`: REST API cho loại công việc
- `JobCategoryService`: Business logic loại công việc
- `JobCategory`: Entity loại công việc

### 4. Feign Clients
**Chức năng**: Giao tiếp giữa các microservice
- `CustomerServiceClient`: Gọi API customer-service
- `JobServiceClient`: Gọi API job-service

### 5. API Gateway
**Chức năng**: Định tuyến request đến các microservice
- `GatewayConfig`: Cấu hình routing
- `CorsConfig`: Cấu hình CORS

## Luồng xử lý tạo hợp đồng

1. Frontend gửi request tạo hợp đồng đến API Gateway
2. API Gateway route request đến customer-contract-service
3. CustomerContractController nhận request
4. CustomerContractService xử lý:
   - Validate thông tin hợp đồng
   - Gọi CustomerServiceClient để lấy thông tin khách hàng
   - Gọi JobServiceClient để lấy thông tin loại công việc
   - Tính toán tổng tiền hợp đồng
   - Lưu hợp đồng, job details và work shifts
5. Trả về thông tin hợp đồng đã tạo

## Đặc điểm kiến trúc Microservice

- **Độc lập**: Mỗi service có database riêng
- **Giao tiếp**: Sử dụng HTTP/REST API qua Feign Client
- **Định tuyến**: API Gateway làm single entry point
- **Scalability**: Có thể scale từng service độc lập
- **Fault Tolerance**: Lỗi một service không ảnh hưởng service khác
