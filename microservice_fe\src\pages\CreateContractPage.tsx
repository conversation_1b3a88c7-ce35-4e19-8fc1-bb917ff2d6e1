import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Box } from '@mui/material';
import { CustomerContractForm } from '../components/contract';
import { CustomerContract } from '../models';
import { contractService } from '../services/contract/contractService';
import { LoadingSpinner, ErrorAlert, SuccessAlert, ContractSuccessAlert } from '../components/common';
import { calculateContractAmount } from '../utils/contractCalculationUtils';

const CreateContractPage: React.FC = () => {
  const navigate = useNavigate();
  const [contract, setContract] = useState<Partial<CustomerContract>>({
    customerId: 0,
    startingDate: '',
    endingDate: '',
    totalAmount: 0,
    description: '',
    jobDetails: [],
    status: 0 // Pending
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [createdContract, setCreatedContract] = useState<CustomerContract | null>(null);
  const [redirectCountdown, setRedirectCountdown] = useState<number | null>(null);

  // Auto-redirect to contract details after successful creation
  useEffect(() => {
    if (createdContract?.id && redirectCountdown === null) {
      // Start countdown from 3 seconds
      setRedirectCountdown(3);
    }
  }, [createdContract, redirectCountdown]);

  // Countdown timer for auto-redirect
  useEffect(() => {
    if (redirectCountdown !== null && redirectCountdown > 0) {
      const timer = setTimeout(() => {
        setRedirectCountdown(redirectCountdown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (redirectCountdown === 0 && createdContract?.id) {
      // Auto-redirect when countdown reaches 0
      navigate(`/contracts/${createdContract.id}`);
    }
  }, [redirectCountdown, createdContract, navigate]);

  const handleContractChange = (updatedContract: Partial<CustomerContract>) => {
    setContract(updatedContract);
  };

  const handleViewDetails = () => {
    if (createdContract?.id) {
      navigate(`/contracts/${createdContract.id}`);
    }
  };

  const handleViewList = () => {
    navigate('/contracts');
  };

  const handleCreateNew = () => {
    // Reset form state
    setContract({
      customerId: 0,
      startingDate: '',
      endingDate: '',
      totalAmount: 0,
      description: '',
      jobDetails: [],
      status: 0
    });
    setCreatedContract(null);
    setSuccess(null);
    setError(null);
    setRedirectCountdown(null);
  };

  const validateContract = (): boolean => {
    if (!contract.customerId || contract.customerId === 0) {
      setError('Vui lòng chọn khách hàng');
      return false;
    }

    // Contract dates are auto-calculated from job details, no need to validate manually
    // Contract address is auto-derived from job details, no need to validate manually
    // Total amount is auto-calculated, no need to validate manually

    if (!contract.jobDetails || contract.jobDetails.length === 0) {
      setError('Vui lòng thêm ít nhất một chi tiết công việc');
      return false;
    }

    // Validate each job detail
    for (const jobDetail of contract.jobDetails) {
      if (!jobDetail.jobCategoryId || jobDetail.jobCategoryId === 0) {
        setError('Vui lòng chọn loại công việc cho tất cả chi tiết công việc');
        return false;
      }

      if (!jobDetail.startDate) {
        setError('Vui lòng nhập ngày bắt đầu cho tất cả chi tiết công việc');
        return false;
      }

      if (!jobDetail.endDate) {
        setError('Vui lòng nhập ngày kết thúc cho tất cả chi tiết công việc');
        return false;
      }

      // Work location is optional - will be auto-assigned if not provided
      // if (!jobDetail.workLocation) {
      //   setError('Vui lòng nhập địa điểm làm việc cho tất cả chi tiết công việc');
      //   return false;
      // }

      if (!jobDetail.workShifts || jobDetail.workShifts.length === 0) {
        setError('Vui lòng thêm ít nhất một ca làm việc cho mỗi chi tiết công việc');
        return false;
      }

      // Validate each work shift
      for (const workShift of jobDetail.workShifts) {
        if (!workShift.startTime) {
          setError('Vui lòng nhập giờ bắt đầu cho tất cả ca làm việc');
          return false;
        }

        if (!workShift.endTime) {
          setError('Vui lòng nhập giờ kết thúc cho tất cả ca làm việc');
          return false;
        }

        if (!workShift.numberOfWorkers || workShift.numberOfWorkers <= 0) {
          setError('Vui lòng nhập số lượng người lao động hợp lệ cho tất cả ca làm việc');
          return false;
        }

        if (workShift.salary === undefined || workShift.salary < 0) {
          setError('Vui lòng nhập mức lương hợp lệ cho tất cả ca làm việc');
          return false;
        }

        if (!workShift.workingDays) {
          setError('Vui lòng chọn ngày làm việc cho tất cả ca làm việc');
          return false;
        }
      }
    }

    return true;
  };

  const handleSubmit = async () => {
    // Prevent double submission with multiple checks
    if (loading) {
      console.log('Contract submission blocked: already loading');
      return;
    }

    // Enhanced duplicate prevention
    const now = Date.now();
    const lastSubmission = localStorage.getItem('lastContractSubmission');
    const submissionKey = `contract_${contract.customerId}_${contract.startingDate}_${contract.endingDate}_${Math.round(contract.totalAmount || 0)}`;
    const lastSubmissionKey = localStorage.getItem('lastContractSubmissionKey');

    // Prevent rapid successive submissions
    if (lastSubmission && (now - parseInt(lastSubmission)) < 2000) {
      console.log('Contract submission blocked: too rapid (within 2 seconds)');
      setError('Vui lòng đợi ít nhất 2 giây trước khi gửi lại');
      return;
    }

    // Prevent duplicate contract submissions
    if (lastSubmissionKey === submissionKey && lastSubmission && (now - parseInt(lastSubmission)) < 60000) {
      console.log('Contract submission blocked: duplicate contract detected');
      setError('Hợp đồng tương tự đã được gửi gần đây. Vui lòng kiểm tra lại.');
      return;
    }

    setError(null);

    if (!validateContract()) {
      return;
    }

    // Ensure total amount is calculated before submitting
    const calculation = calculateContractAmount(contract);
    const contractToSubmit = {
      ...contract,
      totalAmount: calculation.totalAmount
    };

    // Mark submission time and key to prevent rapid resubmission and duplicates
    localStorage.setItem('lastContractSubmission', now.toString());
    localStorage.setItem('lastContractSubmissionKey', submissionKey);
    setLoading(true);

    try {
      console.log('🚀 Submitting contract creation request...', contractToSubmit);

      // Clear any previous error state
      setError(null);

      const createdContract = await contractService.createContract(contractToSubmit as CustomerContract);
      console.log('✅ Contract created successfully:', {
        id: createdContract.id,
        totalAmount: createdContract.totalAmount,
        customerId: createdContract.customerId
      });

      // Verify the contract was actually created with valid data
      if (!createdContract || !createdContract.id) {
        throw new Error('Hợp đồng được tạo nhưng không nhận được thông tin hợp lệ từ máy chủ');
      }

      // Store the created contract for display
      setCreatedContract(createdContract);
      setSuccess(`Hợp đồng #${createdContract.id} đã được tạo thành công!`);

      // Clear the submission timestamp and key on success
      localStorage.removeItem('lastContractSubmission');
      localStorage.removeItem('lastContractSubmissionKey');

      // Set flag to trigger refresh in contracts list
      localStorage.setItem('contractsListNeedsRefresh', 'true');
    } catch (err: any) {
      console.error('❌ Contract creation failed:', err);

      // Provide more specific error messages
      let errorMessage = 'Đã xảy ra lỗi khi tạo hợp đồng';

      if (err.response?.status === 400) {
        errorMessage = 'Dữ liệu hợp đồng không hợp lệ. Vui lòng kiểm tra lại thông tin.';
      } else if (err.response?.status === 500) {
        errorMessage = 'Lỗi máy chủ nội bộ. Vui lòng thử lại sau.';
      } else if (err.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);

      // Clear the submission timestamp on error to allow retry
      localStorage.removeItem('lastContractSubmission');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <LoadingSpinner fullScreen />;
  }

  return (
    <Box>
      {error && <ErrorAlert message={error} />}

      {/* Show success alert with actions if contract was created */}
      {createdContract && (
        <ContractSuccessAlert
          contract={createdContract}
          onViewDetails={handleViewDetails}
          onViewList={handleViewList}
          onCreateNew={handleCreateNew}
          redirectCountdown={redirectCountdown}
        />
      )}

      {/* Show simple success message if no created contract yet */}
      {success && !createdContract && <SuccessAlert message={success} />}

      <CustomerContractForm
        contract={contract}
        onChange={handleContractChange}
        onSubmit={handleSubmit}
        loading={loading}
      />
    </Box>
  );
};

export default CreateContractPage;
