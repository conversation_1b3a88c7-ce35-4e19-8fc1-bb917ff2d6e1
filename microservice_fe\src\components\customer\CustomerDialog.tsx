import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  TextField,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  InputAdornment,
  Avatar,
  useTheme,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import AddIcon from '@mui/icons-material/Add';
import PhoneIcon from '@mui/icons-material/Phone';
import EmailIcon from '@mui/icons-material/Email';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import { Customer } from '../../models';
import { customerService } from '../../services/customer/customerService';
import { LoadingSpinner, ErrorAlert } from '../common';
import CustomerForm from './CustomerForm';
import { parseDate } from '../../utils/dateUtils';

interface CustomerDialogProps {
  open: boolean;
  onClose: () => void;
  onSelectCustomer: (customer: Customer) => void;
}

function CustomerDialog({ open, onClose, onSelectCustomer }: CustomerDialogProps) {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [filteredCustomers, setFilteredCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddCustomerForm, setShowAddCustomerForm] = useState(false);

  const theme = useTheme();

  useEffect(() => {
    const fetchCustomers = async () => {
      try {
        const data = await customerService.getAllCustomers();
        // Sắp xếp theo thời gian tạo mới nhất
        const sortedData = [...data].sort((a, b) => {
          const dateA = parseDate(a.createdAt);
          const dateB = parseDate(b.createdAt);
          return dateB.getTime() - dateA.getTime();
        });
        setCustomers(sortedData);
        setFilteredCustomers(sortedData);
      } catch (err: any) {
        setError(err.message || 'Đã xảy ra lỗi khi tải danh sách khách hàng');
      } finally {
        setLoading(false);
      }
    };

    if (open) {
      fetchCustomers();
    }
  }, [open]);

  const handleSearch = async () => {
    if (!searchTerm.trim()) {
      setFilteredCustomers(customers);
      return;
    }

    try {
      setLoading(true);
      // Tìm kiếm theo cả tên và số điện thoại
      const results = await customerService.searchCustomers(searchTerm, searchTerm);
      setFilteredCustomers(results);

      if (results.length === 0) {
        setError('Không tìm thấy khách hàng nào phù hợp');
      } else {
        setError(null);
      }
    } catch (err: any) {
      console.error('Error searching customers:', err);
      setError(err.message || 'Đã xảy ra lỗi khi tìm kiếm khách hàng');
    } finally {
      setLoading(false);
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    if (!e.target.value.trim()) {
      setFilteredCustomers(customers);
    }
  };

  // Xử lý phím Enter đã được chuyển sang onKeyDown trực tiếp

  const handleAddCustomer = () => {
    setShowAddCustomerForm(true);
  };

  // Xử lý sau khi thêm khách hàng mới
  const handleCustomerAdded = async (_newCustomer: Customer) => {
    setShowAddCustomerForm(false);
    setLoading(true);
    try {
      const data = await customerService.getAllCustomers();
      // Sắp xếp theo thời gian tạo mới nhất
      const sortedData = [...data].sort((a, b) => {
        const dateA = parseDate(a.createdAt);
        const dateB = parseDate(b.createdAt);
        return dateB.getTime() - dateA.getTime();
      });
      setCustomers(sortedData);
      setFilteredCustomers(sortedData);
    } catch (err: any) {
      setError(err.message || 'Đã xảy ra lỗi khi tải lại danh sách khách hàng');
    } finally {
      setLoading(false);
    }
  };

  const handleSelectCustomer = (customer: Customer) => {
    onSelectCustomer(customer);
    onClose();
  };

  if (showAddCustomerForm) {
    return (
      <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
        <DialogTitle>Thêm khách hàng mới</DialogTitle>
        <DialogContent>
          <CustomerForm
            onSave={handleCustomerAdded}
            onCancel={() => setShowAddCustomerForm(false)}
          />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle sx={{
        borderBottom: `1px solid ${theme.palette.divider}`,
        pb: 2
      }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
            Chọn khách hàng
          </Typography>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleAddCustomer}
            sx={{
              borderRadius: '20px',
              px: 2
            }}
          >
            Thêm khách hàng
          </Button>
        </Box>
      </DialogTitle>
      <DialogContent sx={{ pt: 3 }}>
        {error && <ErrorAlert message={error} />}

        <Box sx={{ mb: 3, display: 'flex', gap: 1 }}>
          <TextField
            fullWidth
            placeholder="Tìm kiếm theo tên hoặc số điện thoại"
            value={searchTerm}
            onChange={handleSearchChange}
            onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => e.key === 'Enter' && handleSearch()}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon color="action" />
                </InputAdornment>
              ),
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: '20px',
              }
            }}
          />
          <Button
            variant="outlined"
            onClick={handleSearch}
            sx={{
              borderRadius: '20px',
              px: 3
            }}
          >
            Tìm kiếm
          </Button>
        </Box>

        {loading ? (
          <LoadingSpinner />
        ) : (
          <Box>
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Hiển thị {filteredCustomers.length} khách hàng (sắp xếp theo thời gian tạo mới nhất)
              </Typography>
            </Box>

            <TableContainer
              component={Paper}
              sx={{
                borderRadius: '8px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
                '& .MuiTableCell-head': {
                  backgroundColor: theme.palette.primary.light,
                  color: theme.palette.primary.contrastText,
                  fontWeight: 'bold',
                }
              }}
            >
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Tên khách hàng</TableCell>
                    <TableCell>Công ty</TableCell>
                    <TableCell>Số điện thoại</TableCell>
                    <TableCell>Email</TableCell>
                    <TableCell>Địa chỉ</TableCell>
                    <TableCell align="center">Thao tác</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredCustomers.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} align="center">
                        <Box sx={{ py: 3 }}>
                          <Typography variant="body1">Không tìm thấy khách hàng nào</Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                            Thử tìm kiếm với từ khóa khác hoặc thêm khách hàng mới
                          </Typography>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredCustomers.map((customer) => (
                      <TableRow
                        key={customer.id}
                        hover
                        sx={{
                          cursor: 'pointer',
                          '&:hover': {
                            backgroundColor: theme.palette.action.hover,
                          }
                        }}
                        onClick={() => handleSelectCustomer(customer)}
                      >
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Avatar
                              sx={{
                                bgcolor: theme.palette.primary.main,
                                width: 32,
                                height: 32,
                                mr: 1,
                                fontSize: '0.9rem'
                              }}
                            >
                              {customer.fullName.charAt(0).toUpperCase()}
                            </Avatar>
                            <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                              {customer.fullName}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>{customer.companyName || '-'}</TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <PhoneIcon fontSize="small" sx={{ mr: 0.5, color: theme.palette.text.secondary }} />
                            {customer.phoneNumber}
                          </Box>
                        </TableCell>
                        <TableCell>
                          {customer.email ? (
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <EmailIcon fontSize="small" sx={{ mr: 0.5, color: theme.palette.text.secondary }} />
                              {customer.email}
                            </Box>
                          ) : '-'}
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <LocationOnIcon fontSize="small" sx={{ mr: 0.5, color: theme.palette.text.secondary }} />
                            {customer.address}
                          </Box>
                        </TableCell>
                        <TableCell align="center">
                          <Button
                            variant="contained"
                            size="small"
                            onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                              e.stopPropagation();
                              handleSelectCustomer(customer);
                            }}
                            sx={{
                              borderRadius: '20px',
                              px: 2
                            }}
                          >
                            Chọn
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        )}
      </DialogContent>
      <DialogActions sx={{ borderTop: `1px solid ${theme.palette.divider}`, p: 2 }}>
        <Button
          onClick={onClose}
          color="primary"
          variant="outlined"
          sx={{
            borderRadius: '20px',
            px: 3
          }}
        >
          Đóng
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CustomerDialog;
