import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  Box,
  IconButton,
  Chip,
  Alert,
  Divider
} from '@mui/material';

import CloseIcon from '@mui/icons-material/Close';
import HistoryIcon from '@mui/icons-material/History';
import PaymentIcon from '@mui/icons-material/Payment';

import { CustomerPayment, ContractPayment, PaymentMethodMap } from '../../models';
import { formatCurrency } from '../../utils/formatters';
import { formatDateLocalized } from '../../utils/dateUtils';
import { customerPaymentService } from '../../services';

interface PaymentHistoryDialogProps {
  open: boolean;
  contractId: number | null;
  contractDescription?: string;
  onClose: () => void;
}

const PaymentHistoryDialog: React.FC<PaymentHistoryDialogProps> = ({
  open,
  contractId,
  contractDescription,
  onClose,
}) => {
  const [payments, setPayments] = useState<CustomerPayment[]>([]);
  const [contractPayments, setContractPayments] = useState<ContractPayment[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (open && contractId) {
      fetchPaymentHistory();
    }
  }, [open, contractId]);

  const fetchPaymentHistory = async () => {
    if (!contractId) return;

    setLoading(true);
    setError(null);

    try {
      // Fetch both old payments (one-to-many) and new contract payments (many-to-many)
      const [oldPayments, newContractPayments] = await Promise.all([
        customerPaymentService.getPaymentsByContractId(contractId),
        customerPaymentService.getContractPaymentsByContractId(contractId)
      ]);

      setPayments(oldPayments);
      setContractPayments(newContractPayments);
    } catch (err) {
      console.error('Error fetching payment history:', err);
      setError('Đã xảy ra lỗi khi tải lịch sử thanh toán');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return formatDateLocalized(dateString);
  };

  const getTotalPaid = () => {
    const oldTotal = payments.reduce((sum, payment) => sum + payment.paymentAmount, 0);
    const newTotal = contractPayments.reduce((sum, cp) => sum + cp.allocatedAmount, 0);
    return oldTotal + newTotal;
  };

  const hasPayments = payments.length > 0 || contractPayments.length > 0;

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="md" 
      fullWidth
      PaperProps={{
        sx: { minHeight: '60vh' }
      }}
    >
      <DialogTitle sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        pb: 1
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <HistoryIcon color="primary" />
          <Typography variant="h6">
            Lịch sử thanh toán
          </Typography>
        </Box>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ px: 3 }}>
        {/* Contract Info */}
        <Box sx={{ mb: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
          <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
            Thông tin hợp đồng
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Hợp đồng #{contractId}
          </Typography>
          {contractDescription && (
            <Typography variant="body2" color="text.secondary">
              {contractDescription}
            </Typography>
          )}
        </Box>

        {loading ? (
          <Typography>Đang tải...</Typography>
        ) : error ? (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        ) : !hasPayments ? (
          <Alert severity="info">
            Chưa có thanh toán nào cho hợp đồng này
          </Alert>
        ) : (
          <>
            {/* Old Payments (One-to-Many) */}
            {payments.length > 0 && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <PaymentIcon />
                  Thanh toán đơn lẻ
                </Typography>
                <TableContainer component={Paper} variant="outlined">
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Mã thanh toán</TableCell>
                        <TableCell>Ngày thanh toán</TableCell>
                        <TableCell>Phương thức</TableCell>
                        <TableCell align="right">Số tiền</TableCell>
                        <TableCell>Ghi chú</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {payments.map((payment) => (
                        <TableRow key={payment.id}>
                          <TableCell>#{payment.id}</TableCell>
                          <TableCell>{formatDate(payment.paymentDate?.toString())}</TableCell>
                          <TableCell>
                            <Chip 
                              label={PaymentMethodMap[payment.paymentMethod]} 
                              size="small" 
                              variant="outlined"
                            />
                          </TableCell>
                          <TableCell align="right" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                            {formatCurrency(payment.paymentAmount)}
                          </TableCell>
                          <TableCell>{payment.note || '-'}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            )}

            {/* New Contract Payments (Many-to-Many) */}
            {contractPayments.length > 0 && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <PaymentIcon />
                  Thanh toán nhiều hợp đồng
                </Typography>
                <TableContainer component={Paper} variant="outlined">
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Mã phân bổ</TableCell>
                        <TableCell>Ngày tạo</TableCell>
                        <TableCell align="right">Số tiền phân bổ</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {contractPayments.map((cp) => (
                        <TableRow key={cp.id}>
                          <TableCell>#{cp.id}</TableCell>
                          <TableCell>{formatDate(cp.createdAt)}</TableCell>
                          <TableCell align="right" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                            {formatCurrency(cp.allocatedAmount)}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            )}

            {/* Total Summary */}
            <Divider sx={{ my: 2 }} />
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="h6">
                Tổng số tiền đã thanh toán:
              </Typography>
              <Typography variant="h5" color="success.main" fontWeight="bold">
                {formatCurrency(getTotalPaid())}
              </Typography>
            </Box>
          </>
        )}
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 2 }}>
        <Button
          onClick={onClose}
          color="primary"
          variant="contained"
          startIcon={<CloseIcon />}
        >
          Đóng
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PaymentHistoryDialog;
