import React from 'react';
import { Card } from 'primereact/card';
import { Divider } from 'primereact/divider';
import { TimeBasedRevenue } from '../../models';

interface StatisticsSummaryProps {
  data: TimeBasedRevenue[];
  periodType: string;
  periodLabel?: string;
}

const StatisticsSummary: React.FC<StatisticsSummaryProps> = ({
  data,
  periodType,
  periodLabel
}) => {
  // Calculate total revenue
  const totalRevenue = data.reduce((sum, item) => sum + item.totalRevenue, 0);

  // Calculate total invoices
  const totalInvoices = data.reduce((sum, item) => sum + item.invoiceCount, 0);

  // Format currency
  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      maximumFractionDigits: 0
    }).format(value);
  };

  // Get period type label
  const getPeriodTypeLabel = (): string => {
    switch (periodType) {
      case 'daily':
        return 'ngày';
      case 'monthly':
        return 'tháng';
      case 'quarterly':
        return 'quý';
      case 'yearly':
        return 'năm';
      default:
        return 'khoảng thời gian';
    }
  };

  // Get summary title
  const getSummaryTitle = (): string => {
    if (periodLabel) {
      return `Tổng hợp doanh thu ${getPeriodTypeLabel()} ${periodLabel}`;
    }
    return `Tổng hợp doanh thu theo ${getPeriodTypeLabel()}`;
  };

  return (
    <Card className="mb-3">
      <div className="card-header">
        <h3 className="text-xl font-semibold mb-2">{getSummaryTitle()}</h3>
        <Divider />
      </div>

      <div className="card-body">
        <div className="grid">
          <div className="col-12 md:col-6">
            <div className="p-3 border-round bg-primary text-primary-contrast">
              <div className="text-sm mb-2">Tổng doanh thu</div>
              <div className="text-3xl font-bold">{formatCurrency(totalRevenue)}</div>
            </div>
          </div>

          <div className="col-12 md:col-6">
            <div className="p-3 border-round bg-blue-500 text-white">
              <div className="text-sm mb-2">Tổng số hóa đơn</div>
              <div className="text-3xl font-bold">{totalInvoices}</div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default StatisticsSummary;
