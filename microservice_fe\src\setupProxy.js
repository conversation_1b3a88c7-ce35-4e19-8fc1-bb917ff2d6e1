const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function(app) {
  // Determine target based on environment
  // In Docker: use service name, in local development: use localhost
  const target = process.env.REACT_APP_API_URL || 'http://localhost:8080';

  console.log(`Setting up proxy to: ${target}`);

  // Proxy API calls to the API Gateway
  app.use(
    '/api',
    createProxyMiddleware({
      target: target,
      changeOrigin: true,
      secure: false,
      logLevel: 'info',
      onError: (err, req, res) => {
        console.error('Proxy error:', err);
        res.writeHead(500, {
          'Content-Type': 'text/plain',
        });
        res.end('Proxy error: ' + err.message);
      },
      onProxyReq: (proxyReq, req, res) => {
        console.log(`Proxying ${req.method} ${req.url} to ${target}`);
      },
      onProxyRes: (proxyRes, req, res) => {
        console.log(`Received ${proxyRes.statusCode} from ${req.url}`);
      }
    })
  );
};
