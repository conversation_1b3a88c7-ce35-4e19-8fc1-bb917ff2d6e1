import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { vi } from 'date-fns/locale';

// Create root element
const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

// Render the app - StrictMode disabled to prevent duplicate API calls
root.render(
  <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={vi}>
    <App />
  </LocalizationProvider>
);
