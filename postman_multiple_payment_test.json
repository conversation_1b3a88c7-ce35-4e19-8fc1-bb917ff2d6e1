{"info": {"name": "Customer Payment Service - Multiple Contracts Payment Test", "description": "Test thanh to<PERSON> nhi<PERSON>u hợp đồng cùng lúc"}, "requests": [{"name": "1. Test Health Check", "method": "GET", "url": "http://localhost:8084/actuator/health", "description": "Kiểm tra service có hoạt động không"}, {"name": "2. Get All Payments", "method": "GET", "url": "http://localhost:8084/api/payments", "description": "<PERSON><PERSON><PERSON> danh sách tất cả payments hiện có"}, {"name": "3. Create Multiple Contracts Payment", "method": "POST", "url": "http://localhost:8084/api/payments/multiple-contracts", "headers": {"Content-Type": "application/json"}, "body": {"customerId": 1, "totalAmount": 50000000, "paymentMethod": 0, "note": "<PERSON><PERSON> to<PERSON> nhi<PERSON>u hợp đồng test", "contractPayments": [{"contractId": 7, "allocatedAmount": 15000000}, {"contractId": 9, "allocatedAmount": 24000000}, {"contractId": 10, "allocatedAmount": 11000000}]}, "description": "Test thanh toán cho nhiều hợp đồng cùng lúc"}, {"name": "4. Get Contract Payments by Payment ID", "method": "GET", "url": "http://localhost:8084/api/payments/payment/{paymentId}/contract-payments", "description": "<PERSON><PERSON><PERSON> danh s<PERSON>ch contract payments theo payment ID (thay {paymentId} bằng ID thực)"}, {"name": "5. Get Active Contracts by Customer ID", "method": "GET", "url": "http://localhost:8084/api/payments/customer/1/active-contracts", "description": "<PERSON><PERSON><PERSON> danh s<PERSON>ch hợp đồng active của customer ID 1"}], "test_scenarios": [{"scenario": "Successful Multiple Payment", "steps": ["1. Ch<PERSON>y request 'Get Active Contracts by Customer ID' để lấy danh sách hợp đồng", "2. <PERSON><PERSON><PERSON> 2-3 h<PERSON><PERSON> đồng có remaining amount > 0", "3. <PERSON><PERSON><PERSON> request 'Create Multiple Contracts Payment' với:", "   - customerId: <PERSON> khách hàng", "   - totalAmount: tổng số tiền = sum(allocatedAmount)", "   - contractPayments: danh s<PERSON>ch hợp đồng và số tiền phân bổ", "4. <PERSON><PERSON><PERSON> tra response trả về payment ID", "5. <PERSON><PERSON><PERSON> request 'Get Contract Payments by Payment ID' đ<PERSON> verify"]}, {"scenario": "Error <PERSON> to Test", "cases": [{"case": "Total amount mismatch", "description": "totalAmount != sum(allocatedAmount)", "expected": "Error: Tổng số tiền phân bổ phải bằng tổng số tiền thanh toán"}, {"case": "Invalid contract ID", "description": "contractId không tồn tại", "expected": "Error: <PERSON><PERSON><PERSON><PERSON> tìm thấy hợp đồng <PERSON>"}, {"case": "Amount exceeds remaining", "description": "allocatedAmount > remaining amount", "expected": "Error: <PERSON><PERSON> tiền thanh toán không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá số tiền còn lại"}, {"case": "Empty contract payments", "description": "contractPayments = []", "expected": "Error: <PERSON><PERSON><PERSON> có <PERSON>t nhất một hợp đồng để thanh toán"}]}], "sample_data": {"valid_request": {"customerId": 1, "totalAmount": 50000000, "paymentMethod": 0, "note": "Test payment", "contractPayments": [{"contractId": 7, "allocatedAmount": 15000000}, {"contractId": 9, "allocatedAmount": 24000000}, {"contractId": 10, "allocatedAmount": 11000000}]}, "error_request_total_mismatch": {"customerId": 1, "totalAmount": 40000000, "paymentMethod": 0, "note": "Test error", "contractPayments": [{"contractId": 7, "allocatedAmount": 15000000}, {"contractId": 9, "allocatedAmount": 24000000}]}}, "notes": ["1. <PERSON><PERSON><PERSON> b<PERSON>o customer-payment-service đang chạy trên port 8084", "2. <PERSON><PERSON><PERSON> b<PERSON>o database có dữ liệu customers và contracts", "3. <PERSON><PERSON><PERSON> tra logs backend để debug nếu có lỗi", "4. Payment method: 0=Tiền mặt, 1=Chuyể<PERSON>, 2=Thẻ tín dụng, 3=<PERSON><PERSON><PERSON><PERSON>", "5. <PERSON><PERSON><PERSON> cả amounts phải > 0", "6. Contract phải có status ACTIVE (1) hoặc PENDING (0)"]}