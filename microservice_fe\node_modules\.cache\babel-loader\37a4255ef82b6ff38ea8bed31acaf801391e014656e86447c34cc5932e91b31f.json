{"ast": null, "code": "var _jsxFileName = \"D:\\\\HeThongCongTyQuanLyNhanCong\\\\Microservice_With_Kubernetes\\\\microservice_fe\\\\src\\\\components\\\\payment\\\\CustomerContractList.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Paper, Typography, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Button, Card, CardContent, CardActions, Divider, useTheme, useMediaQuery, Alert, Checkbox } from '@mui/material';\nimport PaymentIcon from '@mui/icons-material/Payment';\nimport HistoryIcon from '@mui/icons-material/History';\nimport { formatCurrency } from '../../utils/formatters';\nimport { formatDateLocalized } from '../../utils/dateUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CustomerContractList = ({\n  contracts,\n  onPaymentClick,\n  onMultiplePaymentClick,\n  onViewHistoryClick\n}) => {\n  _s();\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n\n  // State để quản lý các hợp đồng được chọn\n  const [selectedContracts, setSelectedContracts] = useState([]);\n  const formatDate = dateString => {\n    if (!dateString) return '-';\n    return formatDateLocalized(dateString);\n  };\n\n  // Xử lý chọn/bỏ chọn hợp đồng\n  const handleContractSelect = (contractId, checked) => {\n    if (checked) {\n      setSelectedContracts(prev => [...prev, contractId]);\n    } else {\n      setSelectedContracts(prev => prev.filter(id => id !== contractId));\n    }\n  };\n\n  // Xử lý chọn tất cả\n  const handleSelectAll = checked => {\n    if (checked) {\n      const unpaidContractIds = unpaidContracts.map(contract => contract.id).filter(id => id !== undefined);\n      setSelectedContracts(unpaidContractIds);\n    } else {\n      setSelectedContracts([]);\n    }\n  };\n\n  // Xử lý thanh toán nhiều hợp đồng\n  const handleMultiplePayment = () => {\n    const selectedContractObjects = unpaidContracts.filter(contract => selectedContracts.includes(contract.id));\n    if (onMultiplePaymentClick) {\n      onMultiplePaymentClick(selectedContractObjects);\n    }\n  };\n\n  // Lọc chỉ các hợp đồng còn số tiền cần thanh toán\n  const unpaidContracts = contracts.filter(contract => contract.totalAmount - (contract.totalPaid || 0) > 0);\n  if (contracts.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      sx: {\n        mb: 3\n      },\n      children: \"Kh\\xE1ch h\\xE0ng n\\xE0y ch\\u01B0a c\\xF3 h\\u1EE3p \\u0111\\u1ED3ng n\\xE0o.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this);\n  }\n  if (unpaidContracts.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"success\",\n      sx: {\n        mb: 3\n      },\n      children: \"T\\u1EA5t c\\u1EA3 h\\u1EE3p \\u0111\\u1ED3ng c\\u1EE7a kh\\xE1ch h\\xE0ng n\\xE0y \\u0111\\xE3 \\u0111\\u01B0\\u1EE3c thanh to\\xE1n \\u0111\\u1EA7y \\u0111\\u1EE7.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this);\n  }\n  const allUnpaidSelected = unpaidContracts.length > 0 && unpaidContracts.every(contract => selectedContracts.includes(contract.id));\n  const someUnpaidSelected = unpaidContracts.some(contract => selectedContracts.includes(contract.id));\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: \"Danh s\\xE1ch h\\u1EE3p \\u0111\\u1ED3ng c\\u1EA7n thanh to\\xE1n\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this), selectedContracts.length > 0 && onMultiplePaymentClick && /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        color: \"primary\",\n        startIcon: /*#__PURE__*/_jsxDEV(PaymentIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 24\n        }, this),\n        onClick: handleMultiplePayment,\n        size: \"small\",\n        children: [\"Thanh to\\xE1n (\", selectedContracts.length, \" h\\u1EE3p \\u0111\\u1ED3ng)\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this), isMobile ?\n    /*#__PURE__*/\n    // Mobile view - card list\n    _jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: 2\n      },\n      children: unpaidContracts.map(contract => {\n        const totalPaid = contract.totalPaid || 0;\n        const remaining = contract.totalAmount - totalPaid;\n        const isSelected = selectedContracts.includes(contract.id);\n        return /*#__PURE__*/_jsxDEV(Card, {\n          variant: \"outlined\",\n          children: [/*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                mb: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n                  checked: isSelected,\n                  onChange: e => handleContractSelect(contract.id, e.target.checked),\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  component: \"div\",\n                  children: [\"#\", contract.id]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {\n              sx: {\n                my: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'grid',\n                gridTemplateColumns: '1fr 1fr',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Ng\\xE0y b\\u1EAFt \\u0111\\u1EA7u\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: formatDate(contract.startingDate)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Ng\\xE0y k\\u1EBFt th\\xFAc\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: formatDate(contract.endingDate)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"T\\u1ED5ng gi\\xE1 tr\\u1ECB\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  fontWeight: \"bold\",\n                  children: formatCurrency(contract.totalAmount)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"\\u0110\\xE3 thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: formatCurrency(totalPaid)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mt: 2,\n                p: 1,\n                bgcolor: 'primary.light',\n                borderRadius: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"primary.contrastText\",\n                children: \"C\\xF2n l\\u1EA1i c\\u1EA7n thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                color: \"primary.contrastText\",\n                fontWeight: \"bold\",\n                children: formatCurrency(remaining)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n            sx: {\n              display: 'flex',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"primary\",\n              startIcon: /*#__PURE__*/_jsxDEV(PaymentIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 32\n              }, this),\n              onClick: () => onPaymentClick(contract),\n              sx: {\n                flex: 1\n              },\n              children: \"Thanh to\\xE1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 19\n            }, this), onViewHistoryClick && /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              color: \"info\",\n              startIcon: /*#__PURE__*/_jsxDEV(HistoryIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 34\n              }, this),\n              onClick: () => onViewHistoryClick(contract),\n              size: \"small\",\n              children: \"L\\u1ECBch s\\u1EED\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 17\n          }, this)]\n        }, contract.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 15\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 9\n    }, this) :\n    /*#__PURE__*/\n    // Desktop view - table\n    _jsxDEV(TableContainer, {\n      component: Paper,\n      variant: \"outlined\",\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            sx: {\n              bgcolor: 'primary.light'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              padding: \"checkbox\",\n              children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                indeterminate: someUnpaidSelected && !allUnpaidSelected,\n                checked: allUnpaidSelected,\n                onChange: e => handleSelectAll(e.target.checked),\n                disabled: unpaidContracts.length === 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"M\\xE3 h\\u1EE3p \\u0111\\u1ED3ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Ng\\xE0y b\\u1EAFt \\u0111\\u1EA7u\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Ng\\xE0y k\\u1EBFt th\\xFAc\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"right\",\n              children: \"T\\u1ED5ng gi\\xE1 tr\\u1ECB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"right\",\n              children: \"\\u0110\\xE3 thanh to\\xE1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"right\",\n              children: \"C\\xF2n l\\u1EA1i\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"center\",\n              children: \"Thao t\\xE1c\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: unpaidContracts.map(contract => {\n            const totalPaid = contract.totalPaid || 0;\n            const remaining = contract.totalAmount - totalPaid;\n            const isSelected = selectedContracts.includes(contract.id);\n            return /*#__PURE__*/_jsxDEV(TableRow, {\n              hover: true,\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                padding: \"checkbox\",\n                children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                  checked: isSelected,\n                  onChange: e => handleContractSelect(contract.id, e.target.checked)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [\"#\", contract.id]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: formatDate(contract.startingDate)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: formatDate(contract.endingDate)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: formatCurrency(contract.totalAmount)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: formatCurrency(totalPaid)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                sx: {\n                  fontWeight: 'bold'\n                },\n                children: formatCurrency(remaining)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"center\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    gap: 1,\n                    justifyContent: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outlined\",\n                    color: \"primary\",\n                    size: \"small\",\n                    startIcon: /*#__PURE__*/_jsxDEV(PaymentIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 277,\n                      columnNumber: 38\n                    }, this),\n                    onClick: () => onPaymentClick(contract),\n                    children: \"Thanh to\\xE1n \\u0111\\u01A1n l\\u1EBB\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 273,\n                    columnNumber: 25\n                  }, this), onViewHistoryClick && /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outlined\",\n                    color: \"info\",\n                    size: \"small\",\n                    startIcon: /*#__PURE__*/_jsxDEV(HistoryIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 287,\n                      columnNumber: 40\n                    }, this),\n                    onClick: () => onViewHistoryClick(contract),\n                    children: \"L\\u1ECBch s\\u1EED\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 283,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 21\n              }, this)]\n            }, contract.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 111,\n    columnNumber: 5\n  }, this);\n};\n_s(CustomerContractList, \"wHzthQpKJ9KhVX+EZOb9VN30rcM=\", false, function () {\n  return [useTheme, useMediaQuery];\n});\n_c = CustomerContractList;\nexport default CustomerContractList;\nvar _c;\n$RefreshReg$(_c, \"CustomerContractList\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Paper", "Typography", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Divider", "useTheme", "useMediaQuery", "<PERSON><PERSON>", "Checkbox", "PaymentIcon", "HistoryIcon", "formatCurrency", "formatDateLocalized", "jsxDEV", "_jsxDEV", "CustomerContractList", "contracts", "onPaymentClick", "onMultiplePaymentClick", "onViewHistoryClick", "_s", "theme", "isMobile", "breakpoints", "down", "selectedContracts", "setSelectedContracts", "formatDate", "dateString", "handleContractSelect", "contractId", "checked", "prev", "filter", "id", "handleSelectAll", "unpaidContractIds", "unpaidContracts", "map", "contract", "undefined", "handleMultiplePayment", "selectedContractObjects", "includes", "totalAmount", "totalPaid", "length", "severity", "sx", "mb", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "allUnpaidSelected", "every", "someUnpaidSelected", "some", "display", "justifyContent", "alignItems", "variant", "color", "startIcon", "onClick", "size", "flexDirection", "gap", "remaining", "isSelected", "onChange", "e", "target", "component", "my", "gridTemplateColumns", "startingDate", "endingDate", "fontWeight", "mt", "p", "bgcolor", "borderRadius", "flex", "padding", "indeterminate", "disabled", "align", "hover", "_c", "$RefreshReg$"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/payment/CustomerContractList.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Paper,\n  Typography,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Button,\n  Card,\n  CardContent,\n  CardActions,\n  Divider,\n  useTheme,\n  useMediaQuery,\n  Alert,\n  Checkbox,\n} from '@mui/material';\nimport PaymentIcon from '@mui/icons-material/Payment';\nimport AccountBalanceWalletIcon from '@mui/icons-material/AccountBalanceWallet';\nimport HistoryIcon from '@mui/icons-material/History';\nimport { CustomerContract } from '../../models';\nimport { formatCurrency } from '../../utils/formatters';\nimport { formatDateLocalized } from '../../utils/dateUtils';\n\ninterface CustomerContractListProps {\n  contracts: CustomerContract[];\n  onPaymentClick: (contract: CustomerContract) => void;\n  onMultiplePaymentClick?: (selectedContracts: CustomerContract[]) => void;\n  onViewHistoryClick?: (contract: CustomerContract) => void;\n}\n\nconst CustomerContractList = ({\n  contracts,\n  onPaymentClick,\n  onMultiplePaymentClick,\n  onViewHistoryClick,\n}: CustomerContractListProps): React.ReactElement => {\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n\n  // State để quản lý các hợp đồng được chọn\n  const [selectedContracts, setSelectedContracts] = useState<number[]>([]);\n\n  const formatDate = (dateString?: string) => {\n    if (!dateString) return '-';\n    return formatDateLocalized(dateString);\n  };\n\n  // Xử lý chọn/bỏ chọn hợp đồng\n  const handleContractSelect = (contractId: number, checked: boolean) => {\n    if (checked) {\n      setSelectedContracts(prev => [...prev, contractId]);\n    } else {\n      setSelectedContracts(prev => prev.filter(id => id !== contractId));\n    }\n  };\n\n  // Xử lý chọn tất cả\n  const handleSelectAll = (checked: boolean) => {\n    if (checked) {\n      const unpaidContractIds = unpaidContracts\n        .map(contract => contract.id!)\n        .filter(id => id !== undefined);\n      setSelectedContracts(unpaidContractIds);\n    } else {\n      setSelectedContracts([]);\n    }\n  };\n\n  // Xử lý thanh toán nhiều hợp đồng\n  const handleMultiplePayment = () => {\n    const selectedContractObjects = unpaidContracts.filter(contract =>\n      selectedContracts.includes(contract.id!)\n    );\n    if (onMultiplePaymentClick) {\n      onMultiplePaymentClick(selectedContractObjects);\n    }\n  };\n\n  // Lọc chỉ các hợp đồng còn số tiền cần thanh toán\n  const unpaidContracts = contracts.filter(contract =>\n    (contract.totalAmount! - (contract.totalPaid || 0)) > 0\n  );\n\n  if (contracts.length === 0) {\n    return (\n      <Alert severity=\"info\" sx={{ mb: 3 }}>\n        Khách hàng này chưa có hợp đồng nào.\n      </Alert>\n    );\n  }\n\n  if (unpaidContracts.length === 0) {\n    return (\n      <Alert severity=\"success\" sx={{ mb: 3 }}>\n        Tất cả hợp đồng của khách hàng này đã được thanh toán đầy đủ.\n      </Alert>\n    );\n  }\n\n  const allUnpaidSelected = unpaidContracts.length > 0 &&\n    unpaidContracts.every(contract => selectedContracts.includes(contract.id!));\n  const someUnpaidSelected = unpaidContracts.some(contract =>\n    selectedContracts.includes(contract.id!));\n\n  return (\n    <Box>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n        <Typography variant=\"h6\">\n          Danh sách hợp đồng cần thanh toán\n        </Typography>\n        {selectedContracts.length > 0 && onMultiplePaymentClick && (\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<PaymentIcon />}\n            onClick={handleMultiplePayment}\n            size=\"small\"\n          >\n            Thanh toán ({selectedContracts.length} hợp đồng)\n          </Button>\n        )}\n      </Box>\n\n      {isMobile ? (\n        // Mobile view - card list\n        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>\n          {unpaidContracts.map((contract) => {\n            const totalPaid = contract.totalPaid || 0;\n            const remaining = contract.totalAmount - totalPaid;\n            const isSelected = selectedContracts.includes(contract.id!);\n\n            return (\n              <Card variant=\"outlined\" key={contract.id}>\n                <CardContent>\n                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                      <Checkbox\n                        checked={isSelected}\n                        onChange={(e) => handleContractSelect(contract.id!, e.target.checked)}\n                        size=\"small\"\n                      />\n                      <Typography variant=\"h6\" component=\"div\">\n                        #{contract.id}\n                      </Typography>\n                    </Box>\n                  </Box>\n\n                  <Divider sx={{ my: 1 }} />\n\n                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 1 }}>\n                    <Box>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Ngày bắt đầu\n                      </Typography>\n                      <Typography variant=\"body1\">\n                        {formatDate(contract.startingDate)}\n                      </Typography>\n                    </Box>\n                    <Box>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Ngày kết thúc\n                      </Typography>\n                      <Typography variant=\"body1\">\n                        {formatDate(contract.endingDate)}\n                      </Typography>\n                    </Box>\n                    <Box>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Tổng giá trị\n                      </Typography>\n                      <Typography variant=\"body1\" fontWeight=\"bold\">\n                        {formatCurrency(contract.totalAmount)}\n                      </Typography>\n                    </Box>\n                    <Box>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Đã thanh toán\n                      </Typography>\n                      <Typography variant=\"body1\">\n                        {formatCurrency(totalPaid)}\n                      </Typography>\n                    </Box>\n                  </Box>\n\n                  <Box sx={{ mt: 2, p: 1, bgcolor: 'primary.light', borderRadius: 1 }}>\n                    <Typography variant=\"body2\" color=\"primary.contrastText\">\n                      Còn lại cần thanh toán\n                    </Typography>\n                    <Typography variant=\"h6\" color=\"primary.contrastText\" fontWeight=\"bold\">\n                      {formatCurrency(remaining)}\n                    </Typography>\n                  </Box>\n                </CardContent>\n\n                <CardActions sx={{ display: 'flex', gap: 1 }}>\n                  <Button\n                    variant=\"contained\"\n                    color=\"primary\"\n                    startIcon={<PaymentIcon />}\n                    onClick={() => onPaymentClick(contract)}\n                    sx={{ flex: 1 }}\n                  >\n                    Thanh toán\n                  </Button>\n                  {onViewHistoryClick && (\n                    <Button\n                      variant=\"outlined\"\n                      color=\"info\"\n                      startIcon={<HistoryIcon />}\n                      onClick={() => onViewHistoryClick(contract)}\n                      size=\"small\"\n                    >\n                      Lịch sử\n                    </Button>\n                  )}\n                </CardActions>\n              </Card>\n            );\n          })}\n        </Box>\n      ) : (\n        // Desktop view - table\n        <TableContainer component={Paper} variant=\"outlined\">\n          <Table>\n            <TableHead>\n              <TableRow sx={{ bgcolor: 'primary.light' }}>\n                <TableCell padding=\"checkbox\">\n                  <Checkbox\n                    indeterminate={someUnpaidSelected && !allUnpaidSelected}\n                    checked={allUnpaidSelected}\n                    onChange={(e) => handleSelectAll(e.target.checked)}\n                    disabled={unpaidContracts.length === 0}\n                  />\n                </TableCell>\n                <TableCell>Mã hợp đồng</TableCell>\n                <TableCell>Ngày bắt đầu</TableCell>\n                <TableCell>Ngày kết thúc</TableCell>\n                <TableCell align=\"right\">Tổng giá trị</TableCell>\n                <TableCell align=\"right\">Đã thanh toán</TableCell>\n                <TableCell align=\"right\">Còn lại</TableCell>\n                <TableCell align=\"center\">Thao tác</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {unpaidContracts.map((contract) => {\n                const totalPaid = contract.totalPaid || 0;\n                const remaining = contract.totalAmount - totalPaid;\n                const isSelected = selectedContracts.includes(contract.id!);\n\n                return (\n                  <TableRow key={contract.id} hover>\n                    <TableCell padding=\"checkbox\">\n                      <Checkbox\n                        checked={isSelected}\n                        onChange={(e) => handleContractSelect(contract.id!, e.target.checked)}\n                      />\n                    </TableCell>\n                    <TableCell>#{contract.id}</TableCell>\n                    <TableCell>{formatDate(contract.startingDate)}</TableCell>\n                    <TableCell>{formatDate(contract.endingDate)}</TableCell>\n                    <TableCell align=\"right\">{formatCurrency(contract.totalAmount)}</TableCell>\n                    <TableCell align=\"right\">{formatCurrency(totalPaid)}</TableCell>\n                    <TableCell align=\"right\" sx={{ fontWeight: 'bold' }}>\n                      {formatCurrency(remaining)}\n                    </TableCell>\n                    <TableCell align=\"center\">\n                      <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>\n                        <Button\n                          variant=\"outlined\"\n                          color=\"primary\"\n                          size=\"small\"\n                          startIcon={<PaymentIcon />}\n                          onClick={() => onPaymentClick(contract)}\n                        >\n                          Thanh toán đơn lẻ\n                        </Button>\n                        {onViewHistoryClick && (\n                          <Button\n                            variant=\"outlined\"\n                            color=\"info\"\n                            size=\"small\"\n                            startIcon={<HistoryIcon />}\n                            onClick={() => onViewHistoryClick(contract)}\n                          >\n                            Lịch sử\n                          </Button>\n                        )}\n                      </Box>\n                    </TableCell>\n                  </TableRow>\n                );\n              })}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      )}\n    </Box>\n  );\n};\n\nexport default CustomerContractList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,OAAO,EACPC,QAAQ,EACRC,aAAa,EACbC,KAAK,EACLC,QAAQ,QACH,eAAe;AACtB,OAAOC,WAAW,MAAM,6BAA6B;AAErD,OAAOC,WAAW,MAAM,6BAA6B;AAErD,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,mBAAmB,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAS5D,MAAMC,oBAAoB,GAAGA,CAAC;EAC5BC,SAAS;EACTC,cAAc;EACdC,sBAAsB;EACtBC;AACyB,CAAC,KAAyB;EAAAC,EAAA;EACnD,MAAMC,KAAK,GAAGhB,QAAQ,CAAC,CAAC;EACxB,MAAMiB,QAAQ,GAAGhB,aAAa,CAACe,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;;EAE5D;EACA,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpC,QAAQ,CAAW,EAAE,CAAC;EAExE,MAAMqC,UAAU,GAAIC,UAAmB,IAAK;IAC1C,IAAI,CAACA,UAAU,EAAE,OAAO,GAAG;IAC3B,OAAOhB,mBAAmB,CAACgB,UAAU,CAAC;EACxC,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAGA,CAACC,UAAkB,EAAEC,OAAgB,KAAK;IACrE,IAAIA,OAAO,EAAE;MACXL,oBAAoB,CAACM,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEF,UAAU,CAAC,CAAC;IACrD,CAAC,MAAM;MACLJ,oBAAoB,CAACM,IAAI,IAAIA,IAAI,CAACC,MAAM,CAACC,EAAE,IAAIA,EAAE,KAAKJ,UAAU,CAAC,CAAC;IACpE;EACF,CAAC;;EAED;EACA,MAAMK,eAAe,GAAIJ,OAAgB,IAAK;IAC5C,IAAIA,OAAO,EAAE;MACX,MAAMK,iBAAiB,GAAGC,eAAe,CACtCC,GAAG,CAACC,QAAQ,IAAIA,QAAQ,CAACL,EAAG,CAAC,CAC7BD,MAAM,CAACC,EAAE,IAAIA,EAAE,KAAKM,SAAS,CAAC;MACjCd,oBAAoB,CAACU,iBAAiB,CAAC;IACzC,CAAC,MAAM;MACLV,oBAAoB,CAAC,EAAE,CAAC;IAC1B;EACF,CAAC;;EAED;EACA,MAAMe,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,uBAAuB,GAAGL,eAAe,CAACJ,MAAM,CAACM,QAAQ,IAC7Dd,iBAAiB,CAACkB,QAAQ,CAACJ,QAAQ,CAACL,EAAG,CACzC,CAAC;IACD,IAAIhB,sBAAsB,EAAE;MAC1BA,sBAAsB,CAACwB,uBAAuB,CAAC;IACjD;EACF,CAAC;;EAED;EACA,MAAML,eAAe,GAAGrB,SAAS,CAACiB,MAAM,CAACM,QAAQ,IAC9CA,QAAQ,CAACK,WAAW,IAAKL,QAAQ,CAACM,SAAS,IAAI,CAAC,CAAC,GAAI,CACxD,CAAC;EAED,IAAI7B,SAAS,CAAC8B,MAAM,KAAK,CAAC,EAAE;IAC1B,oBACEhC,OAAA,CAACP,KAAK;MAACwC,QAAQ,EAAC,MAAM;MAACC,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,EAAC;IAEtC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EAEZ;EAEA,IAAIjB,eAAe,CAACS,MAAM,KAAK,CAAC,EAAE;IAChC,oBACEhC,OAAA,CAACP,KAAK;MAACwC,QAAQ,EAAC,SAAS;MAACC,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,EAAC;IAEzC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EAEZ;EAEA,MAAMC,iBAAiB,GAAGlB,eAAe,CAACS,MAAM,GAAG,CAAC,IAClDT,eAAe,CAACmB,KAAK,CAACjB,QAAQ,IAAId,iBAAiB,CAACkB,QAAQ,CAACJ,QAAQ,CAACL,EAAG,CAAC,CAAC;EAC7E,MAAMuB,kBAAkB,GAAGpB,eAAe,CAACqB,IAAI,CAACnB,QAAQ,IACtDd,iBAAiB,CAACkB,QAAQ,CAACJ,QAAQ,CAACL,EAAG,CAAC,CAAC;EAE3C,oBACEpB,OAAA,CAACvB,GAAG;IAAA2D,QAAA,gBACFpC,OAAA,CAACvB,GAAG;MAACyD,EAAE,EAAE;QAAEW,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEZ,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACzFpC,OAAA,CAACrB,UAAU;QAACqE,OAAO,EAAC,IAAI;QAAAZ,QAAA,EAAC;MAEzB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EACZ7B,iBAAiB,CAACqB,MAAM,GAAG,CAAC,IAAI5B,sBAAsB,iBACrDJ,OAAA,CAACd,MAAM;QACL8D,OAAO,EAAC,WAAW;QACnBC,KAAK,EAAC,SAAS;QACfC,SAAS,eAAElD,OAAA,CAACL,WAAW;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC3BW,OAAO,EAAExB,qBAAsB;QAC/ByB,IAAI,EAAC,OAAO;QAAAhB,QAAA,GACb,iBACa,EAACzB,iBAAiB,CAACqB,MAAM,EAAC,2BACxC;MAAA;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAELhC,QAAQ;IAAA;IACP;IACAR,OAAA,CAACvB,GAAG;MAACyD,EAAE,EAAE;QAAEW,OAAO,EAAE,MAAM;QAAEQ,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAE,CAAE;MAAAlB,QAAA,EAC3Db,eAAe,CAACC,GAAG,CAAEC,QAAQ,IAAK;QACjC,MAAMM,SAAS,GAAGN,QAAQ,CAACM,SAAS,IAAI,CAAC;QACzC,MAAMwB,SAAS,GAAG9B,QAAQ,CAACK,WAAW,GAAGC,SAAS;QAClD,MAAMyB,UAAU,GAAG7C,iBAAiB,CAACkB,QAAQ,CAACJ,QAAQ,CAACL,EAAG,CAAC;QAE3D,oBACEpB,OAAA,CAACb,IAAI;UAAC6D,OAAO,EAAC,UAAU;UAAAZ,QAAA,gBACtBpC,OAAA,CAACZ,WAAW;YAAAgD,QAAA,gBACVpC,OAAA,CAACvB,GAAG;cAACyD,EAAE,EAAE;gBAAEW,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE,QAAQ;gBAAEZ,EAAE,EAAE;cAAE,CAAE;cAAAC,QAAA,eACzFpC,OAAA,CAACvB,GAAG;gBAACyD,EAAE,EAAE;kBAAEW,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE,QAAQ;kBAAEO,GAAG,EAAE;gBAAE,CAAE;gBAAAlB,QAAA,gBACzDpC,OAAA,CAACN,QAAQ;kBACPuB,OAAO,EAAEuC,UAAW;kBACpBC,QAAQ,EAAGC,CAAC,IAAK3C,oBAAoB,CAACU,QAAQ,CAACL,EAAE,EAAGsC,CAAC,CAACC,MAAM,CAAC1C,OAAO,CAAE;kBACtEmC,IAAI,EAAC;gBAAO;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,eACFxC,OAAA,CAACrB,UAAU;kBAACqE,OAAO,EAAC,IAAI;kBAACY,SAAS,EAAC,KAAK;kBAAAxB,QAAA,GAAC,GACtC,EAACX,QAAQ,CAACL,EAAE;gBAAA;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENxC,OAAA,CAACV,OAAO;cAAC4C,EAAE,EAAE;gBAAE2B,EAAE,EAAE;cAAE;YAAE;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAE1BxC,OAAA,CAACvB,GAAG;cAACyD,EAAE,EAAE;gBAAEW,OAAO,EAAE,MAAM;gBAAEiB,mBAAmB,EAAE,SAAS;gBAAER,GAAG,EAAE;cAAE,CAAE;cAAAlB,QAAA,gBACnEpC,OAAA,CAACvB,GAAG;gBAAA2D,QAAA,gBACFpC,OAAA,CAACrB,UAAU;kBAACqE,OAAO,EAAC,OAAO;kBAACC,KAAK,EAAC,gBAAgB;kBAAAb,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbxC,OAAA,CAACrB,UAAU;kBAACqE,OAAO,EAAC,OAAO;kBAAAZ,QAAA,EACxBvB,UAAU,CAACY,QAAQ,CAACsC,YAAY;gBAAC;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNxC,OAAA,CAACvB,GAAG;gBAAA2D,QAAA,gBACFpC,OAAA,CAACrB,UAAU;kBAACqE,OAAO,EAAC,OAAO;kBAACC,KAAK,EAAC,gBAAgB;kBAAAb,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbxC,OAAA,CAACrB,UAAU;kBAACqE,OAAO,EAAC,OAAO;kBAAAZ,QAAA,EACxBvB,UAAU,CAACY,QAAQ,CAACuC,UAAU;gBAAC;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNxC,OAAA,CAACvB,GAAG;gBAAA2D,QAAA,gBACFpC,OAAA,CAACrB,UAAU;kBAACqE,OAAO,EAAC,OAAO;kBAACC,KAAK,EAAC,gBAAgB;kBAAAb,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbxC,OAAA,CAACrB,UAAU;kBAACqE,OAAO,EAAC,OAAO;kBAACiB,UAAU,EAAC,MAAM;kBAAA7B,QAAA,EAC1CvC,cAAc,CAAC4B,QAAQ,CAACK,WAAW;gBAAC;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNxC,OAAA,CAACvB,GAAG;gBAAA2D,QAAA,gBACFpC,OAAA,CAACrB,UAAU;kBAACqE,OAAO,EAAC,OAAO;kBAACC,KAAK,EAAC,gBAAgB;kBAAAb,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbxC,OAAA,CAACrB,UAAU;kBAACqE,OAAO,EAAC,OAAO;kBAAAZ,QAAA,EACxBvC,cAAc,CAACkC,SAAS;gBAAC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENxC,OAAA,CAACvB,GAAG;cAACyD,EAAE,EAAE;gBAAEgC,EAAE,EAAE,CAAC;gBAAEC,CAAC,EAAE,CAAC;gBAAEC,OAAO,EAAE,eAAe;gBAAEC,YAAY,EAAE;cAAE,CAAE;cAAAjC,QAAA,gBAClEpC,OAAA,CAACrB,UAAU;gBAACqE,OAAO,EAAC,OAAO;gBAACC,KAAK,EAAC,sBAAsB;gBAAAb,QAAA,EAAC;cAEzD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbxC,OAAA,CAACrB,UAAU;gBAACqE,OAAO,EAAC,IAAI;gBAACC,KAAK,EAAC,sBAAsB;gBAACgB,UAAU,EAAC,MAAM;gBAAA7B,QAAA,EACpEvC,cAAc,CAAC0D,SAAS;cAAC;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eAEdxC,OAAA,CAACX,WAAW;YAAC6C,EAAE,EAAE;cAAEW,OAAO,EAAE,MAAM;cAAES,GAAG,EAAE;YAAE,CAAE;YAAAlB,QAAA,gBAC3CpC,OAAA,CAACd,MAAM;cACL8D,OAAO,EAAC,WAAW;cACnBC,KAAK,EAAC,SAAS;cACfC,SAAS,eAAElD,OAAA,CAACL,WAAW;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BW,OAAO,EAAEA,CAAA,KAAMhD,cAAc,CAACsB,QAAQ,CAAE;cACxCS,EAAE,EAAE;gBAAEoC,IAAI,EAAE;cAAE,CAAE;cAAAlC,QAAA,EACjB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACRnC,kBAAkB,iBACjBL,OAAA,CAACd,MAAM;cACL8D,OAAO,EAAC,UAAU;cAClBC,KAAK,EAAC,MAAM;cACZC,SAAS,eAAElD,OAAA,CAACJ,WAAW;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BW,OAAO,EAAEA,CAAA,KAAM9C,kBAAkB,CAACoB,QAAQ,CAAE;cAC5C2B,IAAI,EAAC,OAAO;cAAAhB,QAAA,EACb;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC;QAAA,GAnFcf,QAAQ,CAACL,EAAE;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoFnC,CAAC;MAEX,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;IAAA;IAEN;IACAxC,OAAA,CAACjB,cAAc;MAAC6E,SAAS,EAAElF,KAAM;MAACsE,OAAO,EAAC,UAAU;MAAAZ,QAAA,eAClDpC,OAAA,CAACpB,KAAK;QAAAwD,QAAA,gBACJpC,OAAA,CAAChB,SAAS;UAAAoD,QAAA,eACRpC,OAAA,CAACf,QAAQ;YAACiD,EAAE,EAAE;cAAEkC,OAAO,EAAE;YAAgB,CAAE;YAAAhC,QAAA,gBACzCpC,OAAA,CAAClB,SAAS;cAACyF,OAAO,EAAC,UAAU;cAAAnC,QAAA,eAC3BpC,OAAA,CAACN,QAAQ;gBACP8E,aAAa,EAAE7B,kBAAkB,IAAI,CAACF,iBAAkB;gBACxDxB,OAAO,EAAEwB,iBAAkB;gBAC3BgB,QAAQ,EAAGC,CAAC,IAAKrC,eAAe,CAACqC,CAAC,CAACC,MAAM,CAAC1C,OAAO,CAAE;gBACnDwD,QAAQ,EAAElD,eAAe,CAACS,MAAM,KAAK;cAAE;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZxC,OAAA,CAAClB,SAAS;cAAAsD,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAClCxC,OAAA,CAAClB,SAAS;cAAAsD,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACnCxC,OAAA,CAAClB,SAAS;cAAAsD,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACpCxC,OAAA,CAAClB,SAAS;cAAC4F,KAAK,EAAC,OAAO;cAAAtC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACjDxC,OAAA,CAAClB,SAAS;cAAC4F,KAAK,EAAC,OAAO;cAAAtC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAClDxC,OAAA,CAAClB,SAAS;cAAC4F,KAAK,EAAC,OAAO;cAAAtC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC5CxC,OAAA,CAAClB,SAAS;cAAC4F,KAAK,EAAC,QAAQ;cAAAtC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZxC,OAAA,CAACnB,SAAS;UAAAuD,QAAA,EACPb,eAAe,CAACC,GAAG,CAAEC,QAAQ,IAAK;YACjC,MAAMM,SAAS,GAAGN,QAAQ,CAACM,SAAS,IAAI,CAAC;YACzC,MAAMwB,SAAS,GAAG9B,QAAQ,CAACK,WAAW,GAAGC,SAAS;YAClD,MAAMyB,UAAU,GAAG7C,iBAAiB,CAACkB,QAAQ,CAACJ,QAAQ,CAACL,EAAG,CAAC;YAE3D,oBACEpB,OAAA,CAACf,QAAQ;cAAmB0F,KAAK;cAAAvC,QAAA,gBAC/BpC,OAAA,CAAClB,SAAS;gBAACyF,OAAO,EAAC,UAAU;gBAAAnC,QAAA,eAC3BpC,OAAA,CAACN,QAAQ;kBACPuB,OAAO,EAAEuC,UAAW;kBACpBC,QAAQ,EAAGC,CAAC,IAAK3C,oBAAoB,CAACU,QAAQ,CAACL,EAAE,EAAGsC,CAAC,CAACC,MAAM,CAAC1C,OAAO;gBAAE;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZxC,OAAA,CAAClB,SAAS;gBAAAsD,QAAA,GAAC,GAAC,EAACX,QAAQ,CAACL,EAAE;cAAA;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrCxC,OAAA,CAAClB,SAAS;gBAAAsD,QAAA,EAAEvB,UAAU,CAACY,QAAQ,CAACsC,YAAY;cAAC;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1DxC,OAAA,CAAClB,SAAS;gBAAAsD,QAAA,EAAEvB,UAAU,CAACY,QAAQ,CAACuC,UAAU;cAAC;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACxDxC,OAAA,CAAClB,SAAS;gBAAC4F,KAAK,EAAC,OAAO;gBAAAtC,QAAA,EAAEvC,cAAc,CAAC4B,QAAQ,CAACK,WAAW;cAAC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC3ExC,OAAA,CAAClB,SAAS;gBAAC4F,KAAK,EAAC,OAAO;gBAAAtC,QAAA,EAAEvC,cAAc,CAACkC,SAAS;cAAC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChExC,OAAA,CAAClB,SAAS;gBAAC4F,KAAK,EAAC,OAAO;gBAACxC,EAAE,EAAE;kBAAE+B,UAAU,EAAE;gBAAO,CAAE;gBAAA7B,QAAA,EACjDvC,cAAc,CAAC0D,SAAS;cAAC;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eACZxC,OAAA,CAAClB,SAAS;gBAAC4F,KAAK,EAAC,QAAQ;gBAAAtC,QAAA,eACvBpC,OAAA,CAACvB,GAAG;kBAACyD,EAAE,EAAE;oBAAEW,OAAO,EAAE,MAAM;oBAAES,GAAG,EAAE,CAAC;oBAAER,cAAc,EAAE;kBAAS,CAAE;kBAAAV,QAAA,gBAC7DpC,OAAA,CAACd,MAAM;oBACL8D,OAAO,EAAC,UAAU;oBAClBC,KAAK,EAAC,SAAS;oBACfG,IAAI,EAAC,OAAO;oBACZF,SAAS,eAAElD,OAAA,CAACL,WAAW;sBAAA0C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAC3BW,OAAO,EAAEA,CAAA,KAAMhD,cAAc,CAACsB,QAAQ,CAAE;oBAAAW,QAAA,EACzC;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACRnC,kBAAkB,iBACjBL,OAAA,CAACd,MAAM;oBACL8D,OAAO,EAAC,UAAU;oBAClBC,KAAK,EAAC,MAAM;oBACZG,IAAI,EAAC,OAAO;oBACZF,SAAS,eAAElD,OAAA,CAACJ,WAAW;sBAAAyC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAC3BW,OAAO,EAAEA,CAAA,KAAM9C,kBAAkB,CAACoB,QAAQ,CAAE;oBAAAW,QAAA,EAC7C;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA,GAtCCf,QAAQ,CAACL,EAAE;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuChB,CAAC;UAEf,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACjB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAClC,EAAA,CA5QIL,oBAAoB;EAAA,QAMVV,QAAQ,EACLC,aAAa;AAAA;AAAAoF,EAAA,GAP1B3E,oBAAoB;AA8Q1B,eAAeA,oBAAoB;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}