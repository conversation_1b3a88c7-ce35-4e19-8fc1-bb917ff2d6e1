import React from 'react';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { TextFieldProps } from '@mui/material';
import { formatDateForInput } from '../../utils/dateUtils';

interface DatePickerFieldProps {
  label: string;
  value: string | Date | undefined;
  onChange: (date: string) => void;
  name?: string;
  required?: boolean;
  disabled?: boolean;
  fullWidth?: boolean;
  error?: boolean;
  helperText?: string;
  sx?: any;
  minDate?: Date;
  maxDate?: Date;
}

/**
 * Tùy chỉnh DatePicker hiển thị ngày tháng theo định dạng Việt Nam (DD/MM/YYYY)
 */
function DatePickerField({
  label,
  value,
  onChange,
  name,
  required = false,
  disabled = false,
  fullWidth = true,
  error = false,
  helperText,
  sx = {},
  minDate,
  maxDate,
}: DatePickerFieldProps) {
  // Chuyển đổi giá trị thành đối tượng Date
  const dateValue = value ? new Date(value) : null;

  // Xử lý sự kiện thay đổi ngày
  const handleDateChange = (date: Date | null) => {
    if (date && !isNaN(date.getTime())) {
      // Chuyển đổi thành chuỗi YYYY-MM-DD để lưu trữ
      const formattedDate = formatDateForInput(date);
      onChange(formattedDate);
    } else {
      onChange('');
    }
  };

  return (
    <DatePicker
      label={label}
      value={dateValue}
      onChange={handleDateChange}
      format="dd/MM/yyyy"
      minDate={minDate}
      maxDate={maxDate}
      slotProps={{
        textField: {
          name,
          required,
          disabled,
          fullWidth,
          error,
          helperText,
          sx: {
            '& .MuiInputLabel-root': {
              transform: 'translate(14px, -9px) scale(0.75)',
              background: '#fff',
              padding: '0 8px'
            },
            '& .MuiInputBase-input': {
              fontFamily: 'inherit',
              fontSize: '1rem'
            },
            ...sx
          } as TextFieldProps['sx'],
        }
      }}
    />
  );
};

export default DatePickerField;
