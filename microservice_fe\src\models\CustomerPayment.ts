import { ContractPayment } from './ContractPayment';

export interface CustomerPayment {
  id?: number;
  paymentDate: string | Date;
  paymentMethod: number;
  paymentAmount: number;
  note?: string;
  // Deprecated: Gi<PERSON> lại để tương thích ngược
  customerContractId?: number;
  customerId: number;
  isDeleted?: boolean;
  createdAt?: string;
  updatedAt?: string;
  // M<PERSON>i quan hệ many-to-many với contracts
  contractPayments?: ContractPayment[];
}

export const PaymentMethodMap: Record<number, string> = {
  0: 'Tiền mặt',
  1: 'Chuyển khoản',
  2: 'Thẻ tín dụng',
  3: 'Khác'
};
