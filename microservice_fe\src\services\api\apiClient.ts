import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

// Kiểm tra môi trường và cấu hình URL phù hợp
const getBaseUrl = () => {
  // Sử dụng proxy setup, không cần base URL
  // Proxy sẽ chuyển tiếp tất cả /api requests tới API Gateway
  return '';
};



// Create a base API client instance
const apiClient: AxiosInstance = axios.create({
  baseURL: getBaseUrl(), // API gateway URL
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Requested-With': 'XMLHttpRequest' // Giúp một số máy chủ nhận biết đây là AJAX request
  },
  timeout: 30000, // Tăng timeout lên 30 seconds
  withCredentials: false // Không gửi cookie trong cross-origin requests
});

// Request interceptor for API calls
apiClient.interceptors.request.use(
  (config) => {
    // Log the request for debugging
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for API calls
apiClient.interceptors.response.use(
  (response) => {
    // Log successful responses for debugging
    console.log(`API Response: ${response.status} ${response.config.url}`, response.data);

    // Kiểm tra nếu response là JSON hợp lệ
    try {
      if (typeof response.data === 'string' && response.data.trim() !== '') {
        console.warn('Response is string, attempting to parse as JSON:', response.data);
        response.data = JSON.parse(response.data);
      }
    } catch (e) {
      console.error('Failed to parse response data as JSON:', e);
      // Don't throw error for successful responses, just log the warning
    }

    // Ensure we have a valid response for successful operations
    if (response.status >= 200 && response.status < 300) {
      console.log('✅ Successful API response:', {
        status: response.status,
        url: response.config.url,
        method: response.config.method,
        dataType: typeof response.data,
        hasData: !!response.data
      });
    }

    return response;
  },
  (error) => {
    // Handle errors globally
    if (axios.isAxiosError(error)) {
      const errorInfo = {
        message: error.message,
        status: error.response?.status,
        url: error.config?.url,
        method: error.config?.method,
        data: error.response?.data,
        code: error.code
      };

      console.error('❌ API Error:', errorInfo);

      // Xử lý thông báo lỗi từ backend
      if (error.response?.data) {
        // Nếu backend trả về thông báo lỗi cụ thể
        if (typeof error.response.data === 'string') {
          error.message = error.response.data;
        } else if (error.response.data.message) {
          error.message = error.response.data.message;
        } else if (error.response.data.error) {
          error.message = error.response.data.error;
        }
      }

      // Log specific error types for debugging
      if (error.response?.status === 200 || error.response?.status === 201) {
        console.warn('⚠️ Received error for successful status code:', error.response.status);
        console.warn('This might indicate a response parsing issue');
      }

      // Chi tiết hơn về các loại lỗi
      if (error.code === 'ECONNABORTED') {
        console.error('Request timeout. The server took too long to respond.');
        error.message = 'Yêu cầu đã hết thời gian chờ. Máy chủ mất quá nhiều thời gian để phản hồi.';
      } else if (error.message.includes('Network Error') || !error.response) {
        console.error('Network error. Please check your connection or the server might be down.');

        // Kiểm tra lỗi CORS
        if (error.message.includes('CORS')) {
          console.error('CORS error detected. This might be a cross-origin issue.');
          error.message = 'Lỗi CORS: Không thể kết nối đến máy chủ do chính sách bảo mật trình duyệt.';
        } else {
          error.message = 'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối của bạn hoặc máy chủ có thể đang gặp sự cố.';
        }
      } else if (error.response?.status === 404) {
        if (!error.response.data?.message) {
          error.message = 'Không tìm thấy tài nguyên yêu cầu.';
        }
      } else if (error.response?.status === 500) {
        if (!error.response.data?.message) {
          error.message = 'Lỗi máy chủ nội bộ. Vui lòng thử lại sau.';
        }
      } else if (error.response?.status === 403) {
        if (!error.response.data?.message) {
          error.message = 'Bạn không có quyền truy cập tài nguyên này.';
        }
      } else if (error.response?.status === 400) {
        // Lỗi validation hoặc business logic từ backend
        if (!error.response.data?.message) {
          error.message = 'Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.';
        }
      }
    } else {
      console.error('Unexpected error:', error);
    }
    return Promise.reject(error);
  }
);

// Generic GET request
export const get = async <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
  try {
    console.log(`Making GET request to: ${getBaseUrl()}${url}`);
    const response: AxiosResponse<T> = await apiClient.get(url, config);
    console.log(`✅ GET request to ${url} succeeded with status:`, response.status);
    return response.data;
  } catch (error) {
    console.error(`❌ GET request failed for ${url}:`, error);
    throw error;
  }
};

// Generic POST request
export const post = async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
  try {
    console.log(`Making POST request to: ${getBaseUrl()}${url}`, data);
    const response: AxiosResponse<T> = await apiClient.post(url, data, config);
    console.log(`✅ POST request to ${url} succeeded with status:`, response.status, response.data);

    // Ensure we return the response data for successful operations
    if (response.status >= 200 && response.status < 300) {
      return response.data;
    } else {
      // This shouldn't happen with axios, but just in case
      throw new Error(`Unexpected response status: ${response.status}`);
    }
  } catch (error) {
    console.error(`❌ POST request failed for ${url}:`, error);

    // For POST requests (create operations), DO NOT use fallback to prevent duplicate creation
    // Only throw the original error to avoid duplicate records
    if (axios.isAxiosError(error)) {
      // Enhance error message with more details
      const errorDetails = {
        status: error.response?.status,
        statusText: error.response?.statusText,
        url: error.config?.url,
        method: error.config?.method,
        data: error.response?.data
      };
      console.error('Detailed error info:', errorDetails);

      if (error.response?.data) {
        if (typeof error.response.data === 'string') {
          error.message = error.response.data;
        } else if (error.response.data.message) {
          error.message = error.response.data.message;
        }
      }
    }
    throw error;
  }
};

// Generic PUT request
export const put = async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
  try {
    console.log(`Making PUT request to: ${getBaseUrl()}${url}`);
    const response: AxiosResponse<T> = await apiClient.put(url, data, config);
    console.log(`PUT request to ${url} succeeded with status:`, response.status);
    return response.data;
  } catch (error) {
    console.error(`PUT request failed for ${url}:`, error);

    // For PUT requests (update operations), DO NOT use fallback to prevent duplicate updates
    // Only throw the original error to avoid duplicate operations
    if (axios.isAxiosError(error) && error.response?.data) {
      if (typeof error.response.data === 'string') {
        error.message = error.response.data;
      } else if (error.response.data.message) {
        error.message = error.response.data.message;
      }
    }
    throw error;
  }
};

// Generic DELETE request
export const del = async <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
  try {
    console.log(`Making DELETE request to: ${getBaseUrl()}${url}`);
    const response: AxiosResponse<T> = await apiClient.delete(url, config);
    console.log(`DELETE request to ${url} succeeded with status:`, response.status);
    return response.data;
  } catch (error) {
    console.error(`DELETE request failed for ${url}:`, error);

    // For DELETE requests, DO NOT use fallback to prevent duplicate deletions
    // Only throw the original error to avoid duplicate operations
    if (axios.isAxiosError(error) && error.response?.data) {
      if (typeof error.response.data === 'string') {
        error.message = error.response.data;
      } else if (error.response.data.message) {
        error.message = error.response.data.message;
      }
    }
    throw error;
  }
};

export default apiClient;
