import { CustomerRevenue, CustomerPayment, TimeBasedRevenue, normalizeCustomerRevenue, normalizeTimeBasedRevenue } from '../../models';
import { get } from '../api/apiClient';
import { formatDateForInput } from '../../utils/dateUtils';

const BASE_URL = '/api/customer-statistics';

export const customerStatisticsService = {
  // Get customer revenue statistics
  getCustomerRevenueStatistics: async (startDate: Date, endDate: Date): Promise<CustomerRevenue[]> => {
    try {
      const formattedStartDate = formatDateToString(startDate);
      const formattedEndDate = formatDateToString(endDate);

      console.log(`Calling API: ${BASE_URL}/revenue?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);

      const result = await get<CustomerRevenue[]>(
        `${BASE_URL}/revenue?startDate=${formattedStartDate}&endDate=${formattedEndDate}`
      );

      console.log('API result:', result);

      if (!result) {
        console.error('API returned null or undefined result');
        return [];
      }

      // Normalize the data using our helper function
      if (Array.isArray(result)) {
        try {
          const normalizedData = result.map(customer => {
            if (!customer) return normalizeCustomerRevenue(null);
            return normalizeCustomerRevenue(customer);
          }).filter(customer => customer !== null);

          console.log('Normalized data:', normalizedData);
          return normalizedData;
        } catch (err) {
          console.error('Error mapping customer revenue data:', err);
          return [];
        }
      }

      // If result is not an array, try to convert it
      if (typeof result === 'object') {
        console.warn('Result is not an array, attempting to convert:', result);
        try {
          const singleItem = normalizeCustomerRevenue(result);
          return [singleItem];
        } catch (err) {
          console.error('Failed to convert object to array:', err);
        }
      }

      console.error('Invalid result format:', result);
      return [];
    } catch (error) {
      console.error('Error in getCustomerRevenueStatistics:', error);
      throw error;
    }
  },

  // Get customer invoices
  getCustomerInvoices: async (customerId: number, startDate: Date, endDate: Date): Promise<CustomerPayment[]> => {
    try {
      const formattedStartDate = formatDateToString(startDate);
      const formattedEndDate = formatDateToString(endDate);

      console.log(`Calling API: ${BASE_URL}/customer/${customerId}/invoices?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);

      const result = await get<CustomerPayment[]>(
        `${BASE_URL}/customer/${customerId}/invoices?startDate=${formattedStartDate}&endDate=${formattedEndDate}`
      );

      console.log('Invoices result:', result);

      if (!result) {
        console.error('API returned null or undefined result for invoices');
        return [];
      }

      // Ensure we have a valid array
      if (Array.isArray(result)) {
        return result.map(invoice => ({
          ...invoice,
          id: invoice.id || 0,
          paymentAmount: typeof invoice.paymentAmount === 'number' ? invoice.paymentAmount : 0,
          paymentDate: invoice.paymentDate ? new Date(invoice.paymentDate) : new Date()
        })).filter(invoice => invoice !== null);
      }

      // If result is not an array but is an object, try to convert it
      if (typeof result === 'object' && result !== null) {
        console.warn('Invoices result is not an array, attempting to convert:', result);
        try {
          return [result as CustomerPayment];
        } catch (err) {
          console.error('Failed to convert invoice object to array:', err);
        }
      }

      console.error('Invalid invoices result format:', result);
      return [];
    } catch (error) {
      console.error('Error in getCustomerInvoices:', error);
      throw error;
    }
  },

  // Get daily revenue statistics
  getDailyRevenueStatistics: async (startDate: Date, endDate: Date): Promise<TimeBasedRevenue[]> => {
    try {
      const formattedStartDate = formatDateToString(startDate);
      const formattedEndDate = formatDateToString(endDate);

      console.log(`Calling API: ${BASE_URL}/revenue/daily?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);

      const result = await get<TimeBasedRevenue[]>(
        `${BASE_URL}/revenue/daily?startDate=${formattedStartDate}&endDate=${formattedEndDate}`
      );

      console.log('API result:', result);

      if (!result) {
        console.error('API returned null or undefined result');
        return [];
      }

      // Normalize the data using our helper function
      if (Array.isArray(result)) {
        try {
          const normalizedData = result.map(item => {
            if (!item) return normalizeTimeBasedRevenue(null);
            return normalizeTimeBasedRevenue(item);
          })
          .filter(item => item !== null)
          .sort((a, b) => {
            const dateA = new Date(a.date);
            const dateB = new Date(b.date);
            return dateA.getTime() - dateB.getTime();
          });

          console.log('Normalized data:', normalizedData);
          return normalizedData;
        } catch (err) {
          console.error('Error mapping daily revenue data:', err);
          return [];
        }
      }

      console.error('API result is not an array:', result);
      return [];
    } catch (error) {
      console.error('Error fetching daily revenue statistics:', error);
      throw error;
    }
  },

  // Get weekly revenue statistics
  getWeeklyRevenueStatistics: async (startDate: Date, endDate: Date): Promise<TimeBasedRevenue[]> => {
    try {
      const formattedStartDate = formatDateToString(startDate);
      const formattedEndDate = formatDateToString(endDate);

      console.log(`Calling API: ${BASE_URL}/revenue/weekly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);

      const result = await get<TimeBasedRevenue[]>(
        `${BASE_URL}/revenue/weekly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`
      );

      console.log('API result:', result);

      if (!result) {
        console.error('API returned null or undefined result');
        return [];
      }

      // Normalize the data using our helper function
      if (Array.isArray(result)) {
        try {
          const normalizedData = result.map(item => {
            if (!item) return normalizeTimeBasedRevenue(null);
            return normalizeTimeBasedRevenue(item);
          }).filter(item => item !== null);

          console.log('Normalized data:', normalizedData);
          return normalizedData;
        } catch (err) {
          console.error('Error mapping weekly revenue data:', err);
          return [];
        }
      }

      console.error('API result is not an array:', result);
      return [];
    } catch (error) {
      console.error('Error fetching weekly revenue statistics:', error);
      throw error;
    }
  },

  // Get monthly revenue statistics
  getMonthlyRevenueStatistics: async (startDate: Date, endDate: Date): Promise<TimeBasedRevenue[]> => {
    try {
      const formattedStartDate = formatDateToString(startDate);
      const formattedEndDate = formatDateToString(endDate);

      console.log(`Calling API: ${BASE_URL}/revenue/monthly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);

      const result = await get<TimeBasedRevenue[]>(
        `${BASE_URL}/revenue/monthly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`
      );

      console.log('API result:', result);

      if (!result) {
        console.error('API returned null or undefined result');
        return [];
      }

      // Normalize the data using our helper function
      if (Array.isArray(result)) {
        try {
          const normalizedData = result.map(item => {
            if (!item) return normalizeTimeBasedRevenue(null);
            return normalizeTimeBasedRevenue(item);
          })
          .filter(item => item !== null)
          .sort((a, b) => {
            const dateA = new Date(a.date);
            const dateB = new Date(b.date);
            return dateA.getTime() - dateB.getTime();
          });

          console.log('Normalized data:', normalizedData);
          return normalizedData;
        } catch (err) {
          console.error('Error mapping monthly revenue data:', err);
          return [];
        }
      }

      console.error('API result is not an array:', result);
      return [];
    } catch (error) {
      console.error('Error fetching monthly revenue statistics:', error);
      throw error;
    }
  },

  // Get yearly revenue statistics
  getYearlyRevenueStatistics: async (startDate: Date, endDate: Date): Promise<TimeBasedRevenue[]> => {
    try {
      const formattedStartDate = formatDateToString(startDate);
      const formattedEndDate = formatDateToString(endDate);

      console.log(`Calling API: ${BASE_URL}/revenue/yearly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);

      const result = await get<TimeBasedRevenue[]>(
        `${BASE_URL}/revenue/yearly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`
      );

      console.log('API result:', result);

      if (!result) {
        console.error('API returned null or undefined result');
        return [];
      }

      // Normalize the data using our helper function
      if (Array.isArray(result)) {
        try {
          const normalizedData = result.map(item => {
            if (!item) return normalizeTimeBasedRevenue(null);
            return normalizeTimeBasedRevenue(item);
          })
          .filter(item => item !== null)
          .sort((a, b) => {
            const dateA = new Date(a.date);
            const dateB = new Date(b.date);
            return dateA.getTime() - dateB.getTime();
          });

          console.log('Normalized data:', normalizedData);
          return normalizedData;
        } catch (err) {
          console.error('Error mapping yearly revenue data:', err);
          return [];
        }
      }

      console.error('API result is not an array:', result);
      return [];
    } catch (error) {
      console.error('Error fetching yearly revenue statistics:', error);
      throw error;
    }
  }
};

// Helper function to format date to string in ISO format (yyyy-MM-dd)
const formatDateToString = (date: Date): string => {
  try {
    // Ensure we have a valid date
    if (!(date instanceof Date) || isNaN(date.getTime())) {
      console.error('Invalid date provided:', date);
      // Return today's date as fallback
      const today = new Date();
      return formatDateForInput(today);
    }

    // Xử lý đặc biệt để tránh vấn đề múi giờ
    // Đặt giờ là 12:00:00 để tránh vấn đề chuyển đổi múi giờ
    const adjustedDate = new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate(),
      12, 0, 0
    );

    // Format to ISO date string using our utility function
    return formatDateForInput(adjustedDate);
  } catch (error) {
    console.error('Error formatting date:', error);
    // Return today's date as fallback
    const today = new Date();
    return formatDateForInput(today);
  }
};
