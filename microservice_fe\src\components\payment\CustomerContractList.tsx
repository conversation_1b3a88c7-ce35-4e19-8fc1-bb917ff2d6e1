import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Card,
  CardContent,
  CardActions,
  Divider,
  useTheme,
  useMediaQuery,
  Alert,
  Checkbox,
} from '@mui/material';
import PaymentIcon from '@mui/icons-material/Payment';
import AccountBalanceWalletIcon from '@mui/icons-material/AccountBalanceWallet';
import HistoryIcon from '@mui/icons-material/History';
import { CustomerContract } from '../../models';
import { formatCurrency } from '../../utils/formatters';
import { formatDateLocalized } from '../../utils/dateUtils';

interface CustomerContractListProps {
  contracts: CustomerContract[];
  onPaymentClick: (contract: CustomerContract) => void;
  onMultiplePaymentClick?: (selectedContracts: CustomerContract[]) => void;
  onViewHistoryClick?: (contract: CustomerContract) => void;
}

const CustomerContractList = ({
  contracts,
  onPaymentClick,
  onMultiplePaymentClick,
  onViewHistoryClick,
}: CustomerContractListProps): React.ReactElement => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // State để quản lý các hợp đồng được chọn
  const [selectedContracts, setSelectedContracts] = useState<number[]>([]);

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return formatDateLocalized(dateString);
  };

  // Xử lý chọn/bỏ chọn hợp đồng
  const handleContractSelect = (contractId: number, checked: boolean) => {
    if (checked) {
      setSelectedContracts(prev => [...prev, contractId]);
    } else {
      setSelectedContracts(prev => prev.filter(id => id !== contractId));
    }
  };

  // Xử lý chọn tất cả
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const unpaidContractIds = unpaidContracts
        .map(contract => contract.id!)
        .filter(id => id !== undefined);
      setSelectedContracts(unpaidContractIds);
    } else {
      setSelectedContracts([]);
    }
  };

  // Xử lý thanh toán nhiều hợp đồng
  const handleMultiplePayment = () => {
    const selectedContractObjects = unpaidContracts.filter(contract =>
      selectedContracts.includes(contract.id!)
    );
    if (onMultiplePaymentClick) {
      onMultiplePaymentClick(selectedContractObjects);
    }
  };

  // Lọc chỉ các hợp đồng còn số tiền cần thanh toán
  const unpaidContracts = contracts.filter(contract =>
    (contract.totalAmount! - (contract.totalPaid || 0)) > 0
  );

  if (contracts.length === 0) {
    return (
      <Alert severity="info" sx={{ mb: 3 }}>
        Khách hàng này chưa có hợp đồng nào.
      </Alert>
    );
  }

  if (unpaidContracts.length === 0) {
    return (
      <Alert severity="success" sx={{ mb: 3 }}>
        Tất cả hợp đồng của khách hàng này đã được thanh toán đầy đủ.
      </Alert>
    );
  }

  const allUnpaidSelected = unpaidContracts.length > 0 &&
    unpaidContracts.every(contract => selectedContracts.includes(contract.id!));
  const someUnpaidSelected = unpaidContracts.some(contract =>
    selectedContracts.includes(contract.id!));

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          Danh sách hợp đồng cần thanh toán
        </Typography>
        {selectedContracts.length > 0 && onMultiplePaymentClick && (
          <Button
            variant="contained"
            color="primary"
            startIcon={<PaymentIcon />}
            onClick={handleMultiplePayment}
            size="small"
          >
            Thanh toán ({selectedContracts.length} hợp đồng)
          </Button>
        )}
      </Box>

      {isMobile ? (
        // Mobile view - card list
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          {unpaidContracts.map((contract) => {
            const totalPaid = contract.totalPaid || 0;
            const remaining = contract.totalAmount - totalPaid;
            const isSelected = selectedContracts.includes(contract.id!);

            return (
              <Card variant="outlined" key={contract.id}>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Checkbox
                        checked={isSelected}
                        onChange={(e) => handleContractSelect(contract.id!, e.target.checked)}
                        size="small"
                      />
                      <Typography variant="h6" component="div">
                        #{contract.id}
                      </Typography>
                    </Box>
                  </Box>

                  <Divider sx={{ my: 1 }} />

                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 1 }}>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Ngày bắt đầu
                      </Typography>
                      <Typography variant="body1">
                        {formatDate(contract.startingDate)}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Ngày kết thúc
                      </Typography>
                      <Typography variant="body1">
                        {formatDate(contract.endingDate)}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Tổng giá trị
                      </Typography>
                      <Typography variant="body1" fontWeight="bold">
                        {formatCurrency(contract.totalAmount)}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Đã thanh toán
                      </Typography>
                      <Typography variant="body1">
                        {formatCurrency(totalPaid)}
                      </Typography>
                    </Box>
                  </Box>

                  <Box sx={{ mt: 2, p: 1, bgcolor: 'primary.light', borderRadius: 1 }}>
                    <Typography variant="body2" color="primary.contrastText">
                      Còn lại cần thanh toán
                    </Typography>
                    <Typography variant="h6" color="primary.contrastText" fontWeight="bold">
                      {formatCurrency(remaining)}
                    </Typography>
                  </Box>
                </CardContent>

                <CardActions sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<PaymentIcon />}
                    onClick={() => onPaymentClick(contract)}
                    sx={{ flex: 1 }}
                  >
                    Thanh toán
                  </Button>
                  {onViewHistoryClick && (
                    <Button
                      variant="outlined"
                      color="info"
                      startIcon={<HistoryIcon />}
                      onClick={() => onViewHistoryClick(contract)}
                      size="small"
                    >
                      Lịch sử
                    </Button>
                  )}
                </CardActions>
              </Card>
            );
          })}
        </Box>
      ) : (
        // Desktop view - table
        <TableContainer component={Paper} variant="outlined">
          <Table>
            <TableHead>
              <TableRow sx={{ bgcolor: 'primary.light' }}>
                <TableCell padding="checkbox">
                  <Checkbox
                    indeterminate={someUnpaidSelected && !allUnpaidSelected}
                    checked={allUnpaidSelected}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    disabled={unpaidContracts.length === 0}
                  />
                </TableCell>
                <TableCell>Mã hợp đồng</TableCell>
                <TableCell>Ngày bắt đầu</TableCell>
                <TableCell>Ngày kết thúc</TableCell>
                <TableCell align="right">Tổng giá trị</TableCell>
                <TableCell align="right">Đã thanh toán</TableCell>
                <TableCell align="right">Còn lại</TableCell>
                <TableCell align="center">Thao tác</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {unpaidContracts.map((contract) => {
                const totalPaid = contract.totalPaid || 0;
                const remaining = contract.totalAmount - totalPaid;
                const isSelected = selectedContracts.includes(contract.id!);

                return (
                  <TableRow key={contract.id} hover>
                    <TableCell padding="checkbox">
                      <Checkbox
                        checked={isSelected}
                        onChange={(e) => handleContractSelect(contract.id!, e.target.checked)}
                      />
                    </TableCell>
                    <TableCell>#{contract.id}</TableCell>
                    <TableCell>{formatDate(contract.startingDate)}</TableCell>
                    <TableCell>{formatDate(contract.endingDate)}</TableCell>
                    <TableCell align="right">{formatCurrency(contract.totalAmount)}</TableCell>
                    <TableCell align="right">{formatCurrency(totalPaid)}</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                      {formatCurrency(remaining)}
                    </TableCell>
                    <TableCell align="center">
                      <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>
                        <Button
                          variant="outlined"
                          color="primary"
                          size="small"
                          startIcon={<PaymentIcon />}
                          onClick={() => onPaymentClick(contract)}
                        >
                          Thanh toán đơn lẻ
                        </Button>
                        {onViewHistoryClick && (
                          <Button
                            variant="outlined"
                            color="info"
                            size="small"
                            startIcon={<HistoryIcon />}
                            onClick={() => onViewHistoryClick(contract)}
                          >
                            Lịch sử
                          </Button>
                        )}
                      </Box>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Box>
  );
};

export default CustomerContractList;
