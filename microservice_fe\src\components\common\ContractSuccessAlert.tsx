import React from 'react';
import {
  Al<PERSON>,
  AlertTitle,
  <PERSON>,
  Button,
  Typography,
  Divider,
  Chip
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Visibility as VisibilityIcon,
  List as ListIcon,
  Add as AddIcon
} from '@mui/icons-material';
import { CustomerContract } from '../../models';

interface ContractSuccessAlertProps {
  contract: CustomerContract;
  onViewDetails: () => void;
  onViewList: () => void;
  onCreateNew: () => void;
  redirectCountdown?: number | null;
}

const ContractSuccessAlert: React.FC<ContractSuccessAlertProps> = ({
  contract,
  onViewDetails,
  onViewList,
  onCreateNew,
  redirectCountdown
}) => {
  return (
    <Alert
      severity="success"
      icon={<CheckCircleIcon fontSize="large" />}
      sx={{
        mb: 3,
        '& .MuiAlert-message': {
          width: '100%'
        }
      }}
    >
      <AlertTitle sx={{ fontSize: '1.2rem', fontWeight: 'bold' }}>
        🎉 Tạo hợp đồng thành công!
      </AlertTitle>

      <Box sx={{ mt: 2 }}>
        <Typography variant="body1" sx={{ mb: 2 }}>
          Hợp đồng <strong>#{contract.id}</strong> đã được tạo thành công với tổng giá trị{' '}
          <Chip
            label={`${contract.totalAmount?.toLocaleString('vi-VN')} VNĐ`}
            color="success"
            variant="outlined"
            size="small"
            sx={{ fontWeight: 'bold' }}
          />
        </Typography>

        <Divider sx={{ my: 2 }} />

        {redirectCountdown !== null && redirectCountdown !== undefined && redirectCountdown > 0 ? (
          <Typography variant="body2" color="primary" sx={{ mb: 2, fontWeight: 'bold' }}>
            🔄 Tự động chuyển đến trang chi tiết hợp đồng trong {redirectCountdown} giây...
          </Typography>
        ) : (
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Bạn muốn làm gì tiếp theo?
          </Typography>
        )}

        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          <Button
            variant="contained"
            startIcon={<VisibilityIcon />}
            onClick={onViewDetails}
            size="small"
          >
            Xem chi tiết hợp đồng
          </Button>

          <Button
            variant="outlined"
            startIcon={<ListIcon />}
            onClick={onViewList}
            size="small"
          >
            Danh sách hợp đồng
          </Button>

          <Button
            variant="outlined"
            startIcon={<AddIcon />}
            onClick={onCreateNew}
            size="small"
          >
            Tạo hợp đồng mới
          </Button>
        </Box>
      </Box>
    </Alert>
  );
};

export default ContractSuccessAlert;
