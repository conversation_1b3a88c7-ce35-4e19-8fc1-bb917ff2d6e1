import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>ield,
  <PERSON>po<PERSON>,
  IconButton,
  Card,
  CardContent,
  Chip,
  Tooltip,
  useTheme,
  Divider,
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import PeopleIcon from '@mui/icons-material/People';
import EventIcon from '@mui/icons-material/Event';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';
import { WorkShift, JobDetail } from '../../models';
import { getWorkingDayOptions, daysArrayToString } from '../../utils/workingDaysUtils';
import { ConfirmDialog } from '../common';
import WorkingDatesPreview from './WorkingDatesPreview';

interface WorkShiftFormProps {
  workShift: Partial<WorkShift>;
  jobDetail: Partial<JobDetail>;
  onChange: (workShift: Partial<WorkShift>) => void;
  onDelete?: () => void;
  showDelete?: boolean;
  shiftIndex?: number;
}

function WorkShiftForm({
  workShift,
  jobDetail,
  onChange,
  onDelete,
  showDelete = false,
  shiftIndex = 0,
}: WorkShiftFormProps) {
  const [selectedDays, setSelectedDays] = useState<number[]>(
    workShift.workingDays ? workShift.workingDays.split(',').map(Number) : []
  );
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const theme = useTheme();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    onChange({
      ...workShift,
      [name]: value,
    });
  };

  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    onChange({
      ...workShift,
      [name]: parseInt(value, 10),
    });
  };

  const handleSalaryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    onChange({
      ...workShift,
      [name]: parseFloat(value),
    });
  };

  const handleDayChange = (day: number) => {
    const newSelectedDays = selectedDays.includes(day)
      ? selectedDays.filter((d) => d !== day)
      : [...selectedDays, day].sort((a, b) => a - b);

    setSelectedDays(newSelectedDays);
    onChange({
      ...workShift,
      workingDays: daysArrayToString(newSelectedDays),
    });
  };

  const dayOptions = getWorkingDayOptions();

  return (
    <Card variant="outlined" sx={{ mb: 2, borderRadius: '8px', border: '1px solid #e0e0e0' }}>
      <CardContent sx={{ p: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <AccessTimeIcon sx={{ mr: 1, color: theme.palette.info.main }} />
            <Typography variant="subtitle1" sx={{ fontWeight: 'bold', color: theme.palette.info.main }}>
              Chi tiết Ca làm việc
            </Typography>
          </Box>
          {showDelete && onDelete && (
            <Tooltip title="Xóa ca làm việc này">
              <IconButton
                color="error"
                onClick={() => setConfirmDialogOpen(true)}
                size="small"
                sx={{
                  border: '1px solid',
                  borderColor: theme.palette.error.light,
                }}
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}

          <ConfirmDialog
            open={confirmDialogOpen}
            title="Xác nhận xóa"
            message="Bạn có chắc chắn muốn xóa ca làm việc này không? Hành động này không thể hoàn tác."
            onConfirm={() => {
              if (onDelete) onDelete();
              setConfirmDialogOpen(false);
            }}
            onCancel={() => setConfirmDialogOpen(false)}
            severity="warning"
          />
        </Box>

        <Divider sx={{ mb: 2 }} />

        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 3 }}>
          <Box sx={{ width: { xs: '100%', sm: '23%' } }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <AccessTimeIcon fontSize="small" sx={{ mr: 0.5, color: theme.palette.text.secondary }} />
              <Typography variant="body2" color="textSecondary">Giờ bắt đầu</Typography>
            </Box>
            <TextField
              fullWidth
              name="startTime"
              value={workShift.startTime || ''}
              onChange={handleInputChange}
              onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                }
              }}
              placeholder="HH:MM"
              size="small"
              required
            />
          </Box>
          <Box sx={{ width: { xs: '100%', sm: '23%' } }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <AccessTimeIcon fontSize="small" sx={{ mr: 0.5, color: theme.palette.text.secondary }} />
              <Typography variant="body2" color="textSecondary">Giờ kết thúc</Typography>
            </Box>
            <TextField
              fullWidth
              name="endTime"
              value={workShift.endTime || ''}
              onChange={handleInputChange}
              onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                }
              }}
              placeholder="HH:MM"
              size="small"
              required
            />
          </Box>
          <Box sx={{ width: { xs: '100%', sm: '23%' } }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <PeopleIcon fontSize="small" sx={{ mr: 0.5, color: theme.palette.text.secondary }} />
              <Typography variant="body2" color="textSecondary">Số lượng người</Typography>
            </Box>
            <TextField
              fullWidth
              name="numberOfWorkers"
              type="number"
              value={workShift.numberOfWorkers || ''}
              onChange={handleNumberChange}
              onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                }
              }}
              slotProps={{
                htmlInput: { min: 1 }
              }}
              size="small"
              required
            />
          </Box>
          <Box sx={{ width: { xs: '100%', sm: '23%' } }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <MonetizationOnIcon fontSize="small" sx={{ mr: 0.5, color: theme.palette.text.secondary }} />
              <Typography variant="body2" color="textSecondary">Lương (VNĐ)</Typography>
            </Box>
            <TextField
              fullWidth
              name="salary"
              type="number"
              value={workShift.salary || ''}
              onChange={handleSalaryChange}
              onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                }
              }}
              slotProps={{
                htmlInput: { min: 0, step: 1000 }
              }}
              size="small"
              required
            />
          </Box>
        </Box>

        <Box>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <EventIcon fontSize="small" sx={{ mr: 0.5, color: theme.palette.text.secondary }} />
            <Typography variant="body2" color="textSecondary">Ngày làm việc trong tuần</Typography>
          </Box>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {dayOptions.map((day) => (
              <Chip
                key={day.value}
                label={day.label}
                onClick={() => handleDayChange(day.value)}
                color={selectedDays.includes(day.value) ? "primary" : "default"}
                variant={selectedDays.includes(day.value) ? "filled" : "outlined"}
                size="small"
                sx={{
                  borderRadius: '16px',
                  '&:hover': {
                    backgroundColor: selectedDays.includes(day.value)
                      ? theme.palette.primary.main
                      : theme.palette.action.hover,
                  }
                }}
              />
            ))}
          </Box>
        </Box>
      </CardContent>

      {/* Working Dates Preview */}
      {workShift.workingDays && jobDetail.startDate && jobDetail.endDate && (
        <WorkingDatesPreview
          workShift={workShift as WorkShift}
          jobDetail={jobDetail as JobDetail}
          shiftIndex={shiftIndex}
        />
      )}
    </Card>
  );
};

export default WorkShiftForm;
