export interface ContractPayment {
  id?: number;
  contractId: number;
  allocatedAmount: number;
  createdAt?: string;
  updatedAt?: string;
  // Thông tin bổ sung để hiển thị
  contractDescription?: string;
  contractTotalAmount?: number;
  contractTotalPaid?: number;
  contractRemainingAmount?: number;
}

export interface ContractPaymentDto {
  contractId: number;
  allocatedAmount: number;
  contractDescription?: string;
  contractTotalAmount?: number;
  contractTotalPaid?: number;
  contractRemainingAmount?: number;
}

export interface CreatePaymentRequest {
  paymentDate?: string;
  paymentMethod: number;
  totalAmount: number;
  note?: string;
  customerId: number;
  contractPayments: ContractPaymentDto[];
}
