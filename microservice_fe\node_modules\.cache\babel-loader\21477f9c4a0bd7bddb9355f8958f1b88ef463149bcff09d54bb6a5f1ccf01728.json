{"ast": null, "code": "var _jsxFileName = \"D:\\\\HeThongCongTyQuanLyNhanCong\\\\Microservice_With_Kubernetes\\\\microservice_fe\\\\src\\\\components\\\\payment\\\\MultipleContractPaymentForm.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, TextField, FormControl, InputLabel, Select, MenuItem, Box, Typography, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, IconButton, Chip, Alert, Divider, InputAdornment } from '@mui/material';\nimport CloseIcon from '@mui/icons-material/Close';\nimport PaymentIcon from '@mui/icons-material/Payment';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport AddIcon from '@mui/icons-material/Add';\nimport { PaymentMethodMap } from '../../models';\nimport { formatCurrency } from '../../utils/formatters';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MultipleContractPaymentForm = ({\n  open,\n  contracts,\n  selectedContracts,\n  customerId,\n  customerName,\n  onClose,\n  onSubmit,\n  loading = false\n}) => {\n  _s();\n  const [paymentMethod, setPaymentMethod] = useState(0);\n  const [note, setNote] = useState('');\n  const [contractPayments, setContractPayments] = useState([]);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    if (open) {\n      // Use selectedContracts if provided, otherwise use all contracts\n      const contractsToUse = selectedContracts && selectedContracts.length > 0 ? selectedContracts : contracts;\n      if (contractsToUse.length > 0) {\n        // Initialize with selected contracts and their remaining amounts\n        const initialPayments = contractsToUse.filter(contract => contract.totalAmount - (contract.totalPaid || 0) > 0) // Only unpaid contracts\n        .map(contract => ({\n          contractId: contract.id,\n          allocatedAmount: contract.totalAmount - (contract.totalPaid || 0),\n          contractDescription: contract.description,\n          contractTotalAmount: contract.totalAmount,\n          contractTotalPaid: contract.totalPaid || 0,\n          contractRemainingAmount: contract.totalAmount - (contract.totalPaid || 0)\n        }));\n        setContractPayments(initialPayments);\n        setPaymentMethod(0);\n        setNote('');\n        setError(null);\n      }\n    }\n  }, [open, contracts, selectedContracts]);\n  const handleAmountChange = (contractId, amount) => {\n    setContractPayments(prev => prev.map(cp => cp.contractId === contractId ? {\n      ...cp,\n      allocatedAmount: amount\n    } : cp));\n    setError(null);\n  };\n  const handleRemoveContract = contractId => {\n    setContractPayments(prev => prev.filter(cp => cp.contractId !== contractId));\n  };\n  const handleAddContract = contract => {\n    const exists = contractPayments.some(cp => cp.contractId === contract.id);\n    if (!exists) {\n      const newPayment = {\n        contractId: contract.id,\n        allocatedAmount: contract.totalAmount - (contract.totalPaid || 0),\n        contractDescription: contract.description,\n        contractTotalAmount: contract.totalAmount,\n        contractTotalPaid: contract.totalPaid || 0,\n        contractRemainingAmount: contract.totalAmount - (contract.totalPaid || 0)\n      };\n      setContractPayments(prev => [...prev, newPayment]);\n    }\n  };\n  const getTotalAmount = () => {\n    return contractPayments.reduce((sum, cp) => sum + cp.allocatedAmount, 0);\n  };\n  const validateForm = () => {\n    if (contractPayments.length === 0) {\n      return 'Phải chọn ít nhất một hợp đồng để thanh toán';\n    }\n    for (const cp of contractPayments) {\n      if (cp.allocatedAmount <= 0) {\n        return 'Số tiền thanh toán phải lớn hơn 0';\n      }\n      if (cp.allocatedAmount > cp.contractRemainingAmount) {\n        return `Số tiền thanh toán cho hợp đồng ${cp.contractId} không được vượt quá số tiền còn lại`;\n      }\n    }\n    return null;\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    const validationError = validateForm();\n    if (validationError) {\n      setError(validationError);\n      return;\n    }\n    const request = {\n      paymentDate: new Date().toISOString(),\n      paymentMethod,\n      totalAmount: getTotalAmount(),\n      note: note || undefined,\n      customerId,\n      contractPayments: contractPayments.map(cp => ({\n        contractId: cp.contractId,\n        allocatedAmount: cp.allocatedAmount\n      }))\n    };\n    onSubmit(request);\n  };\n  const availableContracts = contracts.filter(contract => !contractPayments.some(cp => cp.contractId === contract.id));\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"lg\",\n    fullWidth: true,\n    PaperProps: {\n      sx: {\n        minHeight: '70vh'\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        pb: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(PaymentIcon, {\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: \"Thanh to\\xE1n nhi\\u1EC1u h\\u1EE3p \\u0111\\u1ED3ng\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n        onClick: onClose,\n        size: \"small\",\n        children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      sx: {\n        px: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3,\n          p: 2,\n          bgcolor: 'grey.50',\n          borderRadius: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          fontWeight: \"bold\",\n          gutterBottom: true,\n          children: \"Th\\xF4ng tin kh\\xE1ch h\\xE0ng\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: [customerName, \" (ID: \", customerId, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        id: \"multiple-payment-form\",\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 3,\n            mb: 3,\n            flexDirection: {\n              xs: 'column',\n              md: 'row'\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              flex: 1\n            },\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Ph\\u01B0\\u01A1ng th\\u1EE9c thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: paymentMethod,\n                onChange: e => setPaymentMethod(Number(e.target.value)),\n                label: \"Ph\\u01B0\\u01A1ng th\\u1EE9c thanh to\\xE1n\",\n                children: Object.entries(PaymentMethodMap).map(([key, value]) => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: Number(key),\n                  children: value\n                }, key, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              flex: 1\n            },\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Ghi ch\\xFA\",\n              value: note,\n              onChange: e => setNote(e.target.value),\n              multiline: true,\n              rows: 2,\n              placeholder: \"Nh\\u1EADp ghi ch\\xFA cho thanh to\\xE1n...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          sx: {\n            mt: 3,\n            mb: 2\n          },\n          children: \"Danh s\\xE1ch h\\u1EE3p \\u0111\\u1ED3ng thanh to\\xE1n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this), contractPayments.length > 0 ? /*#__PURE__*/_jsxDEV(TableContainer, {\n          component: Paper,\n          sx: {\n            mb: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"H\\u1EE3p \\u0111\\u1ED3ng\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"right\",\n                  children: \"T\\u1ED5ng ti\\u1EC1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"right\",\n                  children: \"\\u0110\\xE3 thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"right\",\n                  children: \"C\\xF2n l\\u1EA1i\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"right\",\n                  children: \"Thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"center\",\n                  children: \"Thao t\\xE1c\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: contractPayments.map(cp => /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    children: [\"#\", cp.contractId]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 268,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: cp.contractDescription\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 271,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"right\",\n                  children: formatCurrency(cp.contractTotalAmount)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"right\",\n                  children: formatCurrency(cp.contractTotalPaid)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"right\",\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    color: \"primary\",\n                    fontWeight: \"bold\",\n                    children: formatCurrency(cp.contractRemainingAmount)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"right\",\n                  children: /*#__PURE__*/_jsxDEV(TextField, {\n                    type: \"number\",\n                    size: \"small\",\n                    value: cp.allocatedAmount,\n                    onChange: e => handleAmountChange(cp.contractId, Number(e.target.value)),\n                    InputProps: {\n                      endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                        position: \"end\",\n                        children: \"VN\\u0110\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 293,\n                        columnNumber: 43\n                      }, this)\n                    },\n                    sx: {\n                      width: 150\n                    },\n                    inputProps: {\n                      min: 0,\n                      max: cp.contractRemainingAmount\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 287,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"center\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: () => handleRemoveContract(cp.contractId),\n                    color: \"error\",\n                    size: \"small\",\n                    children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 305,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 300,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 23\n                }, this)]\n              }, cp.contractId, true, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mb: 3\n          },\n          children: \"Ch\\u01B0a c\\xF3 h\\u1EE3p \\u0111\\u1ED3ng n\\xE0o \\u0111\\u01B0\\u1EE3c ch\\u1ECDn \\u0111\\u1EC3 thanh to\\xE1n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 13\n        }, this), availableContracts.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Th\\xEAm h\\u1EE3p \\u0111\\u1ED3ng kh\\xE1c\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexWrap: 'wrap',\n              gap: 1\n            },\n            children: availableContracts.map(contract => /*#__PURE__*/_jsxDEV(Chip, {\n              label: `#${contract.id} - ${formatCurrency(contract.totalAmount - (contract.totalPaid || 0))}`,\n              onClick: () => handleAddContract(contract),\n              icon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 27\n              }, this),\n              variant: \"outlined\",\n              clickable: true\n            }, contract.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            my: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"T\\u1ED5ng s\\u1ED1 ti\\u1EC1n thanh to\\xE1n:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            color: \"primary\",\n            fontWeight: \"bold\",\n            children: formatCurrency(getTotalAmount())\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mb: 2\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      sx: {\n        px: 3,\n        py: 2,\n        justifyContent: 'space-between'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: onClose,\n        color: \"inherit\",\n        variant: \"outlined\",\n        startIcon: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 22\n        }, this),\n        children: \"H\\u1EE7y\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"submit\",\n        form: \"multiple-payment-form\",\n        variant: \"contained\",\n        color: \"primary\",\n        startIcon: /*#__PURE__*/_jsxDEV(PaymentIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 22\n        }, this),\n        size: \"large\",\n        disabled: loading || contractPayments.length === 0 || getTotalAmount() <= 0,\n        children: loading ? 'Đang xử lý...' : `Thanh toán ${formatCurrency(getTotalAmount())}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 360,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 177,\n    columnNumber: 5\n  }, this);\n};\n_s(MultipleContractPaymentForm, \"mQ8SJNmi61UtC1lQH3D1UmdED8c=\");\n_c = MultipleContractPaymentForm;\nexport default MultipleContractPaymentForm;\nvar _c;\n$RefreshReg$(_c, \"MultipleContractPaymentForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "Box", "Typography", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "IconButton", "Chip", "<PERSON><PERSON>", "Divider", "InputAdornment", "CloseIcon", "PaymentIcon", "DeleteIcon", "AddIcon", "PaymentMethodMap", "formatCurrency", "jsxDEV", "_jsxDEV", "MultipleContractPaymentForm", "open", "contracts", "selectedContracts", "customerId", "customerName", "onClose", "onSubmit", "loading", "_s", "paymentMethod", "setPaymentMethod", "note", "setNote", "contractPayments", "setContractPayments", "error", "setError", "contractsToUse", "length", "initialPayments", "filter", "contract", "totalAmount", "totalPaid", "map", "contractId", "id", "allocatedAmount", "contractDescription", "description", "contractTotalAmount", "contractTotalPaid", "contractRemainingAmount", "handleAmountChange", "amount", "prev", "cp", "handleRemoveContract", "handleAddContract", "exists", "some", "newPayment", "getTotalAmount", "reduce", "sum", "validateForm", "handleSubmit", "e", "preventDefault", "validationError", "request", "paymentDate", "Date", "toISOString", "undefined", "availableContracts", "max<PERSON><PERSON><PERSON>", "fullWidth", "PaperProps", "sx", "minHeight", "children", "display", "justifyContent", "alignItems", "pb", "gap", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onClick", "size", "px", "mb", "p", "bgcolor", "borderRadius", "fontWeight", "gutterBottom", "flexDirection", "xs", "md", "flex", "required", "value", "onChange", "Number", "target", "label", "Object", "entries", "key", "multiline", "rows", "placeholder", "mt", "component", "align", "type", "InputProps", "endAdornment", "position", "width", "inputProps", "min", "max", "severity", "flexWrap", "icon", "clickable", "my", "py", "startIcon", "form", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/payment/MultipleContractPaymentForm.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  <PERSON>alogActions,\n  Button,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Box,\n  Typography,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  IconButton,\n  Chip,\n  Alert,\n  Divider,\n  Grid,\n  InputAdornment\n} from '@mui/material';\n\nimport CloseIcon from '@mui/icons-material/Close';\nimport PaymentIcon from '@mui/icons-material/Payment';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport AddIcon from '@mui/icons-material/Add';\nimport InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';\n\nimport {\n  CustomerContract,\n  CreatePaymentRequest,\n  ContractPaymentDto,\n  PaymentMethodMap\n} from '../../models';\nimport { formatCurrency } from '../../utils/formatters';\nimport { formatDateLocalized } from '../../utils/dateUtils';\n\ninterface MultipleContractPaymentFormProps {\n  open: boolean;\n  contracts: CustomerContract[];\n  selectedContracts?: CustomerContract[];\n  customerId: number;\n  customerName: string;\n  onClose: () => void;\n  onSubmit: (request: CreatePaymentRequest) => void;\n  loading?: boolean;\n}\n\nconst MultipleContractPaymentForm: React.FC<MultipleContractPaymentFormProps> = ({\n  open,\n  contracts,\n  selectedContracts,\n  customerId,\n  customerName,\n  onClose,\n  onSubmit,\n  loading = false,\n}) => {\n  const [paymentMethod, setPaymentMethod] = useState<number>(0);\n  const [note, setNote] = useState<string>('');\n  const [contractPayments, setContractPayments] = useState<ContractPaymentDto[]>([]);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (open) {\n      // Use selectedContracts if provided, otherwise use all contracts\n      const contractsToUse = selectedContracts && selectedContracts.length > 0 ? selectedContracts : contracts;\n\n      if (contractsToUse.length > 0) {\n        // Initialize with selected contracts and their remaining amounts\n        const initialPayments: ContractPaymentDto[] = contractsToUse\n          .filter(contract => (contract.totalAmount! - (contract.totalPaid || 0)) > 0) // Only unpaid contracts\n          .map(contract => ({\n            contractId: contract.id!,\n            allocatedAmount: contract.totalAmount! - (contract.totalPaid || 0),\n            contractDescription: contract.description,\n            contractTotalAmount: contract.totalAmount,\n            contractTotalPaid: contract.totalPaid || 0,\n            contractRemainingAmount: contract.totalAmount! - (contract.totalPaid || 0)\n          }));\n\n        setContractPayments(initialPayments);\n        setPaymentMethod(0);\n        setNote('');\n        setError(null);\n      }\n    }\n  }, [open, contracts, selectedContracts]);\n\n  const handleAmountChange = (contractId: number, amount: number) => {\n    setContractPayments(prev =>\n      prev.map(cp =>\n        cp.contractId === contractId\n          ? { ...cp, allocatedAmount: amount }\n          : cp\n      )\n    );\n    setError(null);\n  };\n\n  const handleRemoveContract = (contractId: number) => {\n    setContractPayments(prev => prev.filter(cp => cp.contractId !== contractId));\n  };\n\n  const handleAddContract = (contract: CustomerContract) => {\n    const exists = contractPayments.some(cp => cp.contractId === contract.id);\n    if (!exists) {\n      const newPayment: ContractPaymentDto = {\n        contractId: contract.id!,\n        allocatedAmount: contract.totalAmount! - (contract.totalPaid || 0),\n        contractDescription: contract.description,\n        contractTotalAmount: contract.totalAmount,\n        contractTotalPaid: contract.totalPaid || 0,\n        contractRemainingAmount: contract.totalAmount! - (contract.totalPaid || 0)\n      };\n      setContractPayments(prev => [...prev, newPayment]);\n    }\n  };\n\n  const getTotalAmount = () => {\n    return contractPayments.reduce((sum, cp) => sum + cp.allocatedAmount, 0);\n  };\n\n  const validateForm = (): string | null => {\n    if (contractPayments.length === 0) {\n      return 'Phải chọn ít nhất một hợp đồng để thanh toán';\n    }\n\n    for (const cp of contractPayments) {\n      if (cp.allocatedAmount <= 0) {\n        return 'Số tiền thanh toán phải lớn hơn 0';\n      }\n      if (cp.allocatedAmount > cp.contractRemainingAmount!) {\n        return `Số tiền thanh toán cho hợp đồng ${cp.contractId} không được vượt quá số tiền còn lại`;\n      }\n    }\n\n    return null;\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n\n    const validationError = validateForm();\n    if (validationError) {\n      setError(validationError);\n      return;\n    }\n\n    const request: CreatePaymentRequest = {\n      paymentDate: new Date().toISOString(),\n      paymentMethod,\n      totalAmount: getTotalAmount(),\n      note: note || undefined,\n      customerId,\n      contractPayments: contractPayments.map(cp => ({\n        contractId: cp.contractId,\n        allocatedAmount: cp.allocatedAmount\n      }))\n    };\n\n    onSubmit(request);\n  };\n\n  const availableContracts = contracts.filter(contract =>\n    !contractPayments.some(cp => cp.contractId === contract.id)\n  );\n\n  return (\n    <Dialog\n      open={open}\n      onClose={onClose}\n      maxWidth=\"lg\"\n      fullWidth\n      PaperProps={{\n        sx: { minHeight: '70vh' }\n      }}\n    >\n      <DialogTitle sx={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        pb: 1\n      }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n          <PaymentIcon color=\"primary\" />\n          <Typography variant=\"h6\">\n            Thanh toán nhiều hợp đồng\n          </Typography>\n        </Box>\n        <IconButton onClick={onClose} size=\"small\">\n          <CloseIcon />\n        </IconButton>\n      </DialogTitle>\n\n      <DialogContent sx={{ px: 3 }}>\n        {/* Customer Info */}\n        <Box sx={{ mb: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>\n          <Typography variant=\"subtitle1\" fontWeight=\"bold\" gutterBottom>\n            Thông tin khách hàng\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            {customerName} (ID: {customerId})\n          </Typography>\n        </Box>\n\n        <form id=\"multiple-payment-form\" onSubmit={handleSubmit}>\n          {/* Payment Method */}\n          <Box sx={{ display: 'flex', gap: 3, mb: 3, flexDirection: { xs: 'column', md: 'row' } }}>\n            <Box sx={{ flex: 1 }}>\n              <FormControl fullWidth required>\n                <InputLabel>Phương thức thanh toán</InputLabel>\n                <Select\n                  value={paymentMethod}\n                  onChange={(e) => setPaymentMethod(Number(e.target.value))}\n                  label=\"Phương thức thanh toán\"\n                >\n                  {Object.entries(PaymentMethodMap).map(([key, value]) => (\n                    <MenuItem key={key} value={Number(key)}>\n                      {value}\n                    </MenuItem>\n                  ))}\n                </Select>\n              </FormControl>\n            </Box>\n            <Box sx={{ flex: 1 }}>\n              <TextField\n                fullWidth\n                label=\"Ghi chú\"\n                value={note}\n                onChange={(e) => setNote(e.target.value)}\n                multiline\n                rows={2}\n                placeholder=\"Nhập ghi chú cho thanh toán...\"\n              />\n            </Box>\n          </Box>\n\n          {/* Contract Payments Table */}\n          <Typography variant=\"h6\" gutterBottom sx={{ mt: 3, mb: 2 }}>\n            Danh sách hợp đồng thanh toán\n          </Typography>\n\n          {contractPayments.length > 0 ? (\n            <TableContainer component={Paper} sx={{ mb: 3 }}>\n              <Table>\n                <TableHead>\n                  <TableRow>\n                    <TableCell>Hợp đồng</TableCell>\n                    <TableCell align=\"right\">Tổng tiền</TableCell>\n                    <TableCell align=\"right\">Đã thanh toán</TableCell>\n                    <TableCell align=\"right\">Còn lại</TableCell>\n                    <TableCell align=\"right\">Thanh toán</TableCell>\n                    <TableCell align=\"center\">Thao tác</TableCell>\n                  </TableRow>\n                </TableHead>\n                <TableBody>\n                  {contractPayments.map((cp) => (\n                    <TableRow key={cp.contractId}>\n                      <TableCell>\n                        <Typography variant=\"body2\" fontWeight=\"bold\">\n                          #{cp.contractId}\n                        </Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          {cp.contractDescription}\n                        </Typography>\n                      </TableCell>\n                      <TableCell align=\"right\">\n                        {formatCurrency(cp.contractTotalAmount!)}\n                      </TableCell>\n                      <TableCell align=\"right\">\n                        {formatCurrency(cp.contractTotalPaid!)}\n                      </TableCell>\n                      <TableCell align=\"right\">\n                        <Typography color=\"primary\" fontWeight=\"bold\">\n                          {formatCurrency(cp.contractRemainingAmount!)}\n                        </Typography>\n                      </TableCell>\n                      <TableCell align=\"right\">\n                        <TextField\n                          type=\"number\"\n                          size=\"small\"\n                          value={cp.allocatedAmount}\n                          onChange={(e) => handleAmountChange(cp.contractId, Number(e.target.value))}\n                          InputProps={{\n                            endAdornment: <InputAdornment position=\"end\">VNĐ</InputAdornment>,\n                          }}\n                          sx={{ width: 150 }}\n                          inputProps={{ min: 0, max: cp.contractRemainingAmount }}\n                        />\n                      </TableCell>\n                      <TableCell align=\"center\">\n                        <IconButton\n                          onClick={() => handleRemoveContract(cp.contractId)}\n                          color=\"error\"\n                          size=\"small\"\n                        >\n                          <DeleteIcon />\n                        </IconButton>\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </TableContainer>\n          ) : (\n            <Alert severity=\"info\" sx={{ mb: 3 }}>\n              Chưa có hợp đồng nào được chọn để thanh toán\n            </Alert>\n          )}\n\n          {/* Add Contract Section */}\n          {availableContracts.length > 0 && (\n            <Box sx={{ mb: 3 }}>\n              <Typography variant=\"subtitle1\" gutterBottom>\n                Thêm hợp đồng khác\n              </Typography>\n              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>\n                {availableContracts.map((contract) => (\n                  <Chip\n                    key={contract.id}\n                    label={`#${contract.id} - ${formatCurrency(contract.totalAmount! - (contract.totalPaid || 0))}`}\n                    onClick={() => handleAddContract(contract)}\n                    icon={<AddIcon />}\n                    variant=\"outlined\"\n                    clickable\n                  />\n                ))}\n              </Box>\n            </Box>\n          )}\n\n          {/* Total Amount */}\n          <Divider sx={{ my: 2 }} />\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n            <Typography variant=\"h6\">\n              Tổng số tiền thanh toán:\n            </Typography>\n            <Typography variant=\"h5\" color=\"primary\" fontWeight=\"bold\">\n              {formatCurrency(getTotalAmount())}\n            </Typography>\n          </Box>\n\n          {/* Error Message */}\n          {error && (\n            <Alert severity=\"error\" sx={{ mb: 2 }}>\n              {error}\n            </Alert>\n          )}\n        </form>\n      </DialogContent>\n\n      <DialogActions sx={{ px: 3, py: 2, justifyContent: 'space-between' }}>\n        <Button\n          onClick={onClose}\n          color=\"inherit\"\n          variant=\"outlined\"\n          startIcon={<CloseIcon />}\n        >\n          Hủy\n        </Button>\n        <Button\n          type=\"submit\"\n          form=\"multiple-payment-form\"\n          variant=\"contained\"\n          color=\"primary\"\n          startIcon={<PaymentIcon />}\n          size=\"large\"\n          disabled={loading || contractPayments.length === 0 || getTotalAmount() <= 0}\n        >\n          {loading ? 'Đang xử lý...' : `Thanh toán ${formatCurrency(getTotalAmount())}`}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default MultipleContractPaymentForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,UAAU,EACVC,IAAI,EACJC,KAAK,EACLC,OAAO,EAEPC,cAAc,QACT,eAAe;AAEtB,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,OAAO,MAAM,yBAAyB;AAG7C,SAIEC,gBAAgB,QACX,cAAc;AACrB,SAASC,cAAc,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAcxD,MAAMC,2BAAuE,GAAGA,CAAC;EAC/EC,IAAI;EACJC,SAAS;EACTC,iBAAiB;EACjBC,UAAU;EACVC,YAAY;EACZC,OAAO;EACPC,QAAQ;EACRC,OAAO,GAAG;AACZ,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG7C,QAAQ,CAAS,CAAC,CAAC;EAC7D,MAAM,CAAC8C,IAAI,EAAEC,OAAO,CAAC,GAAG/C,QAAQ,CAAS,EAAE,CAAC;EAC5C,MAAM,CAACgD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjD,QAAQ,CAAuB,EAAE,CAAC;EAClF,MAAM,CAACkD,KAAK,EAAEC,QAAQ,CAAC,GAAGnD,QAAQ,CAAgB,IAAI,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd,IAAIkC,IAAI,EAAE;MACR;MACA,MAAMiB,cAAc,GAAGf,iBAAiB,IAAIA,iBAAiB,CAACgB,MAAM,GAAG,CAAC,GAAGhB,iBAAiB,GAAGD,SAAS;MAExG,IAAIgB,cAAc,CAACC,MAAM,GAAG,CAAC,EAAE;QAC7B;QACA,MAAMC,eAAqC,GAAGF,cAAc,CACzDG,MAAM,CAACC,QAAQ,IAAKA,QAAQ,CAACC,WAAW,IAAKD,QAAQ,CAACE,SAAS,IAAI,CAAC,CAAC,GAAI,CAAC,CAAC,CAAC;QAAA,CAC5EC,GAAG,CAACH,QAAQ,KAAK;UAChBI,UAAU,EAAEJ,QAAQ,CAACK,EAAG;UACxBC,eAAe,EAAEN,QAAQ,CAACC,WAAW,IAAKD,QAAQ,CAACE,SAAS,IAAI,CAAC,CAAC;UAClEK,mBAAmB,EAAEP,QAAQ,CAACQ,WAAW;UACzCC,mBAAmB,EAAET,QAAQ,CAACC,WAAW;UACzCS,iBAAiB,EAAEV,QAAQ,CAACE,SAAS,IAAI,CAAC;UAC1CS,uBAAuB,EAAEX,QAAQ,CAACC,WAAW,IAAKD,QAAQ,CAACE,SAAS,IAAI,CAAC;QAC3E,CAAC,CAAC,CAAC;QAELT,mBAAmB,CAACK,eAAe,CAAC;QACpCT,gBAAgB,CAAC,CAAC,CAAC;QACnBE,OAAO,CAAC,EAAE,CAAC;QACXI,QAAQ,CAAC,IAAI,CAAC;MAChB;IACF;EACF,CAAC,EAAE,CAAChB,IAAI,EAAEC,SAAS,EAAEC,iBAAiB,CAAC,CAAC;EAExC,MAAM+B,kBAAkB,GAAGA,CAACR,UAAkB,EAAES,MAAc,KAAK;IACjEpB,mBAAmB,CAACqB,IAAI,IACtBA,IAAI,CAACX,GAAG,CAACY,EAAE,IACTA,EAAE,CAACX,UAAU,KAAKA,UAAU,GACxB;MAAE,GAAGW,EAAE;MAAET,eAAe,EAAEO;IAAO,CAAC,GAClCE,EACN,CACF,CAAC;IACDpB,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;EAED,MAAMqB,oBAAoB,GAAIZ,UAAkB,IAAK;IACnDX,mBAAmB,CAACqB,IAAI,IAAIA,IAAI,CAACf,MAAM,CAACgB,EAAE,IAAIA,EAAE,CAACX,UAAU,KAAKA,UAAU,CAAC,CAAC;EAC9E,CAAC;EAED,MAAMa,iBAAiB,GAAIjB,QAA0B,IAAK;IACxD,MAAMkB,MAAM,GAAG1B,gBAAgB,CAAC2B,IAAI,CAACJ,EAAE,IAAIA,EAAE,CAACX,UAAU,KAAKJ,QAAQ,CAACK,EAAE,CAAC;IACzE,IAAI,CAACa,MAAM,EAAE;MACX,MAAME,UAA8B,GAAG;QACrChB,UAAU,EAAEJ,QAAQ,CAACK,EAAG;QACxBC,eAAe,EAAEN,QAAQ,CAACC,WAAW,IAAKD,QAAQ,CAACE,SAAS,IAAI,CAAC,CAAC;QAClEK,mBAAmB,EAAEP,QAAQ,CAACQ,WAAW;QACzCC,mBAAmB,EAAET,QAAQ,CAACC,WAAW;QACzCS,iBAAiB,EAAEV,QAAQ,CAACE,SAAS,IAAI,CAAC;QAC1CS,uBAAuB,EAAEX,QAAQ,CAACC,WAAW,IAAKD,QAAQ,CAACE,SAAS,IAAI,CAAC;MAC3E,CAAC;MACDT,mBAAmB,CAACqB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEM,UAAU,CAAC,CAAC;IACpD;EACF,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,OAAO7B,gBAAgB,CAAC8B,MAAM,CAAC,CAACC,GAAG,EAAER,EAAE,KAAKQ,GAAG,GAAGR,EAAE,CAACT,eAAe,EAAE,CAAC,CAAC;EAC1E,CAAC;EAED,MAAMkB,YAAY,GAAGA,CAAA,KAAqB;IACxC,IAAIhC,gBAAgB,CAACK,MAAM,KAAK,CAAC,EAAE;MACjC,OAAO,8CAA8C;IACvD;IAEA,KAAK,MAAMkB,EAAE,IAAIvB,gBAAgB,EAAE;MACjC,IAAIuB,EAAE,CAACT,eAAe,IAAI,CAAC,EAAE;QAC3B,OAAO,mCAAmC;MAC5C;MACA,IAAIS,EAAE,CAACT,eAAe,GAAGS,EAAE,CAACJ,uBAAwB,EAAE;QACpD,OAAO,mCAAmCI,EAAE,CAACX,UAAU,sCAAsC;MAC/F;IACF;IAEA,OAAO,IAAI;EACb,CAAC;EAED,MAAMqB,YAAY,GAAIC,CAAkB,IAAK;IAC3CA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,MAAMC,eAAe,GAAGJ,YAAY,CAAC,CAAC;IACtC,IAAII,eAAe,EAAE;MACnBjC,QAAQ,CAACiC,eAAe,CAAC;MACzB;IACF;IAEA,MAAMC,OAA6B,GAAG;MACpCC,WAAW,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACrC5C,aAAa;MACba,WAAW,EAAEoB,cAAc,CAAC,CAAC;MAC7B/B,IAAI,EAAEA,IAAI,IAAI2C,SAAS;MACvBnD,UAAU;MACVU,gBAAgB,EAAEA,gBAAgB,CAACW,GAAG,CAACY,EAAE,KAAK;QAC5CX,UAAU,EAAEW,EAAE,CAACX,UAAU;QACzBE,eAAe,EAAES,EAAE,CAACT;MACtB,CAAC,CAAC;IACJ,CAAC;IAEDrB,QAAQ,CAAC4C,OAAO,CAAC;EACnB,CAAC;EAED,MAAMK,kBAAkB,GAAGtD,SAAS,CAACmB,MAAM,CAACC,QAAQ,IAClD,CAACR,gBAAgB,CAAC2B,IAAI,CAACJ,EAAE,IAAIA,EAAE,CAACX,UAAU,KAAKJ,QAAQ,CAACK,EAAE,CAC5D,CAAC;EAED,oBACE5B,OAAA,CAAC/B,MAAM;IACLiC,IAAI,EAAEA,IAAK;IACXK,OAAO,EAAEA,OAAQ;IACjBmD,QAAQ,EAAC,IAAI;IACbC,SAAS;IACTC,UAAU,EAAE;MACVC,EAAE,EAAE;QAAEC,SAAS,EAAE;MAAO;IAC1B,CAAE;IAAAC,QAAA,gBAEF/D,OAAA,CAAC9B,WAAW;MAAC2F,EAAE,EAAE;QACfG,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBC,EAAE,EAAE;MACN,CAAE;MAAAJ,QAAA,gBACA/D,OAAA,CAACrB,GAAG;QAACkF,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAEE,GAAG,EAAE;QAAE,CAAE;QAAAL,QAAA,gBACzD/D,OAAA,CAACN,WAAW;UAAC2E,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/BzE,OAAA,CAACpB,UAAU;UAAC8F,OAAO,EAAC,IAAI;UAAAX,QAAA,EAAC;QAEzB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACNzE,OAAA,CAACZ,UAAU;QAACuF,OAAO,EAAEpE,OAAQ;QAACqE,IAAI,EAAC,OAAO;QAAAb,QAAA,eACxC/D,OAAA,CAACP,SAAS;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEdzE,OAAA,CAAC7B,aAAa;MAAC0F,EAAE,EAAE;QAAEgB,EAAE,EAAE;MAAE,CAAE;MAAAd,QAAA,gBAE3B/D,OAAA,CAACrB,GAAG;QAACkF,EAAE,EAAE;UAAEiB,EAAE,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;UAAEC,OAAO,EAAE,SAAS;UAAEC,YAAY,EAAE;QAAE,CAAE;QAAAlB,QAAA,gBAC5D/D,OAAA,CAACpB,UAAU;UAAC8F,OAAO,EAAC,WAAW;UAACQ,UAAU,EAAC,MAAM;UAACC,YAAY;UAAApB,QAAA,EAAC;QAE/D;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbzE,OAAA,CAACpB,UAAU;UAAC8F,OAAO,EAAC,OAAO;UAACL,KAAK,EAAC,gBAAgB;UAAAN,QAAA,GAC/CzD,YAAY,EAAC,QAAM,EAACD,UAAU,EAAC,GAClC;QAAA;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAENzE,OAAA;QAAM4B,EAAE,EAAC,uBAAuB;QAACpB,QAAQ,EAAEwC,YAAa;QAAAe,QAAA,gBAEtD/D,OAAA,CAACrB,GAAG;UAACkF,EAAE,EAAE;YAAEG,OAAO,EAAE,MAAM;YAAEI,GAAG,EAAE,CAAC;YAAEU,EAAE,EAAE,CAAC;YAAEM,aAAa,EAAE;cAAEC,EAAE,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAM;UAAE,CAAE;UAAAvB,QAAA,gBACtF/D,OAAA,CAACrB,GAAG;YAACkF,EAAE,EAAE;cAAE0B,IAAI,EAAE;YAAE,CAAE;YAAAxB,QAAA,eACnB/D,OAAA,CAACzB,WAAW;cAACoF,SAAS;cAAC6B,QAAQ;cAAAzB,QAAA,gBAC7B/D,OAAA,CAACxB,UAAU;gBAAAuF,QAAA,EAAC;cAAsB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC/CzE,OAAA,CAACvB,MAAM;gBACLgH,KAAK,EAAE9E,aAAc;gBACrB+E,QAAQ,EAAGzC,CAAC,IAAKrC,gBAAgB,CAAC+E,MAAM,CAAC1C,CAAC,CAAC2C,MAAM,CAACH,KAAK,CAAC,CAAE;gBAC1DI,KAAK,EAAC,0CAAwB;gBAAA9B,QAAA,EAE7B+B,MAAM,CAACC,OAAO,CAAClG,gBAAgB,CAAC,CAAC6B,GAAG,CAAC,CAAC,CAACsE,GAAG,EAAEP,KAAK,CAAC,kBACjDzF,OAAA,CAACtB,QAAQ;kBAAW+G,KAAK,EAAEE,MAAM,CAACK,GAAG,CAAE;kBAAAjC,QAAA,EACpC0B;gBAAK,GADOO,GAAG;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAER,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACNzE,OAAA,CAACrB,GAAG;YAACkF,EAAE,EAAE;cAAE0B,IAAI,EAAE;YAAE,CAAE;YAAAxB,QAAA,eACnB/D,OAAA,CAAC1B,SAAS;cACRqF,SAAS;cACTkC,KAAK,EAAC,YAAS;cACfJ,KAAK,EAAE5E,IAAK;cACZ6E,QAAQ,EAAGzC,CAAC,IAAKnC,OAAO,CAACmC,CAAC,CAAC2C,MAAM,CAACH,KAAK,CAAE;cACzCQ,SAAS;cACTC,IAAI,EAAE,CAAE;cACRC,WAAW,EAAC;YAAgC;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNzE,OAAA,CAACpB,UAAU;UAAC8F,OAAO,EAAC,IAAI;UAACS,YAAY;UAACtB,EAAE,EAAE;YAAEuC,EAAE,EAAE,CAAC;YAAEtB,EAAE,EAAE;UAAE,CAAE;UAAAf,QAAA,EAAC;QAE5D;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZ1D,gBAAgB,CAACK,MAAM,GAAG,CAAC,gBAC1BpB,OAAA,CAAChB,cAAc;UAACqH,SAAS,EAAElH,KAAM;UAAC0E,EAAE,EAAE;YAAEiB,EAAE,EAAE;UAAE,CAAE;UAAAf,QAAA,eAC9C/D,OAAA,CAACnB,KAAK;YAAAkF,QAAA,gBACJ/D,OAAA,CAACf,SAAS;cAAA8E,QAAA,eACR/D,OAAA,CAACd,QAAQ;gBAAA6E,QAAA,gBACP/D,OAAA,CAACjB,SAAS;kBAAAgF,QAAA,EAAC;gBAAQ;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC/BzE,OAAA,CAACjB,SAAS;kBAACuH,KAAK,EAAC,OAAO;kBAAAvC,QAAA,EAAC;gBAAS;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC9CzE,OAAA,CAACjB,SAAS;kBAACuH,KAAK,EAAC,OAAO;kBAAAvC,QAAA,EAAC;gBAAa;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAClDzE,OAAA,CAACjB,SAAS;kBAACuH,KAAK,EAAC,OAAO;kBAAAvC,QAAA,EAAC;gBAAO;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC5CzE,OAAA,CAACjB,SAAS;kBAACuH,KAAK,EAAC,OAAO;kBAAAvC,QAAA,EAAC;gBAAU;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC/CzE,OAAA,CAACjB,SAAS;kBAACuH,KAAK,EAAC,QAAQ;kBAAAvC,QAAA,EAAC;gBAAQ;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACZzE,OAAA,CAAClB,SAAS;cAAAiF,QAAA,EACPhD,gBAAgB,CAACW,GAAG,CAAEY,EAAE,iBACvBtC,OAAA,CAACd,QAAQ;gBAAA6E,QAAA,gBACP/D,OAAA,CAACjB,SAAS;kBAAAgF,QAAA,gBACR/D,OAAA,CAACpB,UAAU;oBAAC8F,OAAO,EAAC,OAAO;oBAACQ,UAAU,EAAC,MAAM;oBAAAnB,QAAA,GAAC,GAC3C,EAACzB,EAAE,CAACX,UAAU;kBAAA;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACbzE,OAAA,CAACpB,UAAU;oBAAC8F,OAAO,EAAC,SAAS;oBAACL,KAAK,EAAC,gBAAgB;oBAAAN,QAAA,EACjDzB,EAAE,CAACR;kBAAmB;oBAAAwC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACZzE,OAAA,CAACjB,SAAS;kBAACuH,KAAK,EAAC,OAAO;kBAAAvC,QAAA,EACrBjE,cAAc,CAACwC,EAAE,CAACN,mBAAoB;gBAAC;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACZzE,OAAA,CAACjB,SAAS;kBAACuH,KAAK,EAAC,OAAO;kBAAAvC,QAAA,EACrBjE,cAAc,CAACwC,EAAE,CAACL,iBAAkB;gBAAC;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC,eACZzE,OAAA,CAACjB,SAAS;kBAACuH,KAAK,EAAC,OAAO;kBAAAvC,QAAA,eACtB/D,OAAA,CAACpB,UAAU;oBAACyF,KAAK,EAAC,SAAS;oBAACa,UAAU,EAAC,MAAM;oBAAAnB,QAAA,EAC1CjE,cAAc,CAACwC,EAAE,CAACJ,uBAAwB;kBAAC;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACZzE,OAAA,CAACjB,SAAS;kBAACuH,KAAK,EAAC,OAAO;kBAAAvC,QAAA,eACtB/D,OAAA,CAAC1B,SAAS;oBACRiI,IAAI,EAAC,QAAQ;oBACb3B,IAAI,EAAC,OAAO;oBACZa,KAAK,EAAEnD,EAAE,CAACT,eAAgB;oBAC1B6D,QAAQ,EAAGzC,CAAC,IAAKd,kBAAkB,CAACG,EAAE,CAACX,UAAU,EAAEgE,MAAM,CAAC1C,CAAC,CAAC2C,MAAM,CAACH,KAAK,CAAC,CAAE;oBAC3Ee,UAAU,EAAE;sBACVC,YAAY,eAAEzG,OAAA,CAACR,cAAc;wBAACkH,QAAQ,EAAC,KAAK;wBAAA3C,QAAA,EAAC;sBAAG;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAgB;oBAClE,CAAE;oBACFZ,EAAE,EAAE;sBAAE8C,KAAK,EAAE;oBAAI,CAAE;oBACnBC,UAAU,EAAE;sBAAEC,GAAG,EAAE,CAAC;sBAAEC,GAAG,EAAExE,EAAE,CAACJ;oBAAwB;kBAAE;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZzE,OAAA,CAACjB,SAAS;kBAACuH,KAAK,EAAC,QAAQ;kBAAAvC,QAAA,eACvB/D,OAAA,CAACZ,UAAU;oBACTuF,OAAO,EAAEA,CAAA,KAAMpC,oBAAoB,CAACD,EAAE,CAACX,UAAU,CAAE;oBACnD0C,KAAK,EAAC,OAAO;oBACbO,IAAI,EAAC,OAAO;oBAAAb,QAAA,eAEZ/D,OAAA,CAACL,UAAU;sBAAA2E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GAzCCnC,EAAE,CAACX,UAAU;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0ClB,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,gBAEjBzE,OAAA,CAACV,KAAK;UAACyH,QAAQ,EAAC,MAAM;UAAClD,EAAE,EAAE;YAAEiB,EAAE,EAAE;UAAE,CAAE;UAAAf,QAAA,EAAC;QAEtC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR,EAGAhB,kBAAkB,CAACrC,MAAM,GAAG,CAAC,iBAC5BpB,OAAA,CAACrB,GAAG;UAACkF,EAAE,EAAE;YAAEiB,EAAE,EAAE;UAAE,CAAE;UAAAf,QAAA,gBACjB/D,OAAA,CAACpB,UAAU;YAAC8F,OAAO,EAAC,WAAW;YAACS,YAAY;YAAApB,QAAA,EAAC;UAE7C;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbzE,OAAA,CAACrB,GAAG;YAACkF,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEgD,QAAQ,EAAE,MAAM;cAAE5C,GAAG,EAAE;YAAE,CAAE;YAAAL,QAAA,EACpDN,kBAAkB,CAAC/B,GAAG,CAAEH,QAAQ,iBAC/BvB,OAAA,CAACX,IAAI;cAEHwG,KAAK,EAAE,IAAItE,QAAQ,CAACK,EAAE,MAAM9B,cAAc,CAACyB,QAAQ,CAACC,WAAW,IAAKD,QAAQ,CAACE,SAAS,IAAI,CAAC,CAAC,CAAC,EAAG;cAChGkD,OAAO,EAAEA,CAAA,KAAMnC,iBAAiB,CAACjB,QAAQ,CAAE;cAC3C0F,IAAI,eAAEjH,OAAA,CAACJ,OAAO;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAClBC,OAAO,EAAC,UAAU;cAClBwC,SAAS;YAAA,GALJ3F,QAAQ,CAACK,EAAE;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAMjB,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGDzE,OAAA,CAACT,OAAO;UAACsE,EAAE,EAAE;YAAEsD,EAAE,EAAE;UAAE;QAAE;UAAA7C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1BzE,OAAA,CAACrB,GAAG;UAACkF,EAAE,EAAE;YAAEG,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE,QAAQ;YAAEY,EAAE,EAAE;UAAE,CAAE;UAAAf,QAAA,gBACzF/D,OAAA,CAACpB,UAAU;YAAC8F,OAAO,EAAC,IAAI;YAAAX,QAAA,EAAC;UAEzB;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbzE,OAAA,CAACpB,UAAU;YAAC8F,OAAO,EAAC,IAAI;YAACL,KAAK,EAAC,SAAS;YAACa,UAAU,EAAC,MAAM;YAAAnB,QAAA,EACvDjE,cAAc,CAAC8C,cAAc,CAAC,CAAC;UAAC;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,EAGLxD,KAAK,iBACJjB,OAAA,CAACV,KAAK;UAACyH,QAAQ,EAAC,OAAO;UAAClD,EAAE,EAAE;YAAEiB,EAAE,EAAE;UAAE,CAAE;UAAAf,QAAA,EACnC9C;QAAK;UAAAqD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAEhBzE,OAAA,CAAC5B,aAAa;MAACyF,EAAE,EAAE;QAAEgB,EAAE,EAAE,CAAC;QAAEuC,EAAE,EAAE,CAAC;QAAEnD,cAAc,EAAE;MAAgB,CAAE;MAAAF,QAAA,gBACnE/D,OAAA,CAAC3B,MAAM;QACLsG,OAAO,EAAEpE,OAAQ;QACjB8D,KAAK,EAAC,SAAS;QACfK,OAAO,EAAC,UAAU;QAClB2C,SAAS,eAAErH,OAAA,CAACP,SAAS;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAV,QAAA,EAC1B;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTzE,OAAA,CAAC3B,MAAM;QACLkI,IAAI,EAAC,QAAQ;QACbe,IAAI,EAAC,uBAAuB;QAC5B5C,OAAO,EAAC,WAAW;QACnBL,KAAK,EAAC,SAAS;QACfgD,SAAS,eAAErH,OAAA,CAACN,WAAW;UAAA4E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC3BG,IAAI,EAAC,OAAO;QACZ2C,QAAQ,EAAE9G,OAAO,IAAIM,gBAAgB,CAACK,MAAM,KAAK,CAAC,IAAIwB,cAAc,CAAC,CAAC,IAAI,CAAE;QAAAmB,QAAA,EAE3EtD,OAAO,GAAG,eAAe,GAAG,cAAcX,cAAc,CAAC8C,cAAc,CAAC,CAAC,CAAC;MAAE;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAAC/D,EAAA,CAvUIT,2BAAuE;AAAAuH,EAAA,GAAvEvH,2BAAuE;AAyU7E,eAAeA,2BAA2B;AAAC,IAAAuH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}