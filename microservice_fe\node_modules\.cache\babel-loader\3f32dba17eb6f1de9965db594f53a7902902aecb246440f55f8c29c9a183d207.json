{"ast": null, "code": "import { get, post } from '../api/apiClient';\nconst BASE_URL = '/api/customer-payment';\nexport const customerPaymentService = {\n  // L<PERSON>y tất cả các thanh toán\n  getAllPayments: async () => {\n    return get(BASE_URL);\n  },\n  // L<PERSON>y thanh toán theo ID\n  getPaymentById: async id => {\n    return get(`${BASE_URL}/payment/${id}`);\n  },\n  // Tạo thanh toán mới\n  createPayment: async payment => {\n    console.log('🚀 Payment service: Creating payment...', {\n      contractId: payment.customerContractId,\n      customerId: payment.customerId,\n      amount: payment.paymentAmount,\n      method: payment.paymentMethod,\n      paymentDate: payment.paymentDate\n    });\n    const result = await post(BASE_URL, payment);\n    console.log('✅ Payment service: Payment created successfully:', {\n      id: result.id,\n      amount: result.paymentAmount,\n      contractId: result.customerContractId\n    });\n    return result;\n  },\n  // Tìm kiếm khách hàng\n  searchCustomers: async (fullName, phoneNumber) => {\n    let url = `${BASE_URL}/customer/search`;\n    const params = [];\n    if (fullName) params.push(`fullName=${encodeURIComponent(fullName)}`);\n    if (phoneNumber) params.push(`phoneNumber=${encodeURIComponent(phoneNumber)}`);\n    if (params.length > 0) url += `?${params.join('&')}`;\n    return get(url);\n  },\n  // Lấy thanh toán theo khách hàng\n  getPaymentsByCustomerId: async customerId => {\n    return get(`${BASE_URL}/customer/${customerId}`);\n  },\n  // Lấy thanh toán theo hợp đồng\n  getPaymentsByContractId: async contractId => {\n    return get(`${BASE_URL}/contract/${contractId}`);\n  },\n  // Lấy hợp đồng đang hoạt động của khách hàng\n  getActiveContractsByCustomerId: async customerId => {\n    return get(`${BASE_URL}/customer/${customerId}/active-contracts`);\n  },\n  // Lấy thông tin thanh toán của hợp đồng\n  getContractPaymentInfo: async contractId => {\n    return get(`${BASE_URL}/contract/${contractId}/payment-info`);\n  },\n  // Lấy tổng số tiền đã thanh toán của hợp đồng\n  getTotalPaidAmountByContractId: async contractId => {\n    return get(`${BASE_URL}/contract/${contractId}/total-paid`);\n  },\n  // Lấy số tiền còn lại của hợp đồng\n  getRemainingAmountByContractId: async contractId => {\n    return get(`${BASE_URL}/contract/${contractId}/remaining-amount`);\n  },\n  // API mới - Tạo thanh toán cho nhiều hợp đồng\n  createPaymentWithMultipleContracts: async request => {\n    console.log('🚀 Payment service: Creating payment with multiple contracts...', {\n      customerId: request.customerId,\n      totalAmount: request.totalAmount,\n      contractCount: request.contractPayments.length,\n      contracts: request.contractPayments.map(cp => ({\n        contractId: cp.contractId,\n        amount: cp.allocatedAmount\n      })),\n      fullRequest: request\n    });\n    try {\n      var _result$contractPayme;\n      const result = await post(`${BASE_URL}/multiple-contracts`, request);\n      console.log('✅ Payment service: Payment with multiple contracts created successfully:', {\n        id: result.id,\n        totalAmount: result.paymentAmount,\n        contractPayments: ((_result$contractPayme = result.contractPayments) === null || _result$contractPayme === void 0 ? void 0 : _result$contractPayme.length) || 0\n      });\n      return result;\n    } catch (error) {\n      console.error('❌ Payment service: Error creating payment with multiple contracts:', error);\n      throw error;\n    }\n  },\n  // Lấy danh sách thanh toán theo payment ID\n  getContractPaymentsByPaymentId: async paymentId => {\n    return get(`${BASE_URL}/payment/${paymentId}/contract-payments`);\n  },\n  // Lấy danh sách thanh toán theo contract ID\n  getContractPaymentsByContractId: async contractId => {\n    return get(`${BASE_URL}/contract/${contractId}/contract-payments`);\n  }\n};", "map": {"version": 3, "names": ["get", "post", "BASE_URL", "customerPaymentService", "getAllPayments", "getPaymentById", "id", "createPayment", "payment", "console", "log", "contractId", "customerContractId", "customerId", "amount", "paymentAmount", "method", "paymentMethod", "paymentDate", "result", "searchCustomers", "fullName", "phoneNumber", "url", "params", "push", "encodeURIComponent", "length", "join", "getPaymentsByCustomerId", "getPaymentsByContractId", "getActiveContractsByCustomerId", "getContractPaymentInfo", "getTotalPaidAmountByContractId", "getRemainingAmountByContractId", "createPaymentWithMultipleContracts", "request", "totalAmount", "contractCount", "contractPayments", "contracts", "map", "cp", "allocatedAmount", "fullRequest", "_result$contractPayme", "error", "getContractPaymentsByPaymentId", "paymentId", "getContractPaymentsByContractId"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/services/payment/customerPaymentService.ts"], "sourcesContent": ["import { CustomerPayment, Customer, CustomerContract, ContractPayment, CreatePaymentRequest } from '../../models';\nimport { get, post } from '../api/apiClient';\n\nconst BASE_URL = '/api/customer-payment';\n\nexport const customerPaymentService = {\n  // L<PERSON>y tất cả các thanh toán\n  getAllPayments: async (): Promise<CustomerPayment[]> => {\n    return get<CustomerPayment[]>(BASE_URL);\n  },\n\n  // Lấy thanh toán theo ID\n  getPaymentById: async (id: number): Promise<CustomerPayment> => {\n    return get<CustomerPayment>(`${BASE_URL}/payment/${id}`);\n  },\n\n  // Tạo thanh toán mới\n  createPayment: async (payment: CustomerPayment): Promise<CustomerPayment> => {\n    console.log('🚀 Payment service: Creating payment...', {\n      contractId: payment.customerContractId,\n      customerId: payment.customerId,\n      amount: payment.paymentAmount,\n      method: payment.paymentMethod,\n      paymentDate: payment.paymentDate\n    });\n\n    const result = await post<CustomerPayment>(BASE_URL, payment);\n\n    console.log('✅ Payment service: Payment created successfully:', {\n      id: result.id,\n      amount: result.paymentAmount,\n      contractId: result.customerContractId\n    });\n\n    return result;\n  },\n\n  // Tìm kiếm khách hàng\n  searchCustomers: async (fullName?: string, phoneNumber?: string): Promise<Customer[]> => {\n    let url = `${BASE_URL}/customer/search`;\n    const params = [];\n    if (fullName) params.push(`fullName=${encodeURIComponent(fullName)}`);\n    if (phoneNumber) params.push(`phoneNumber=${encodeURIComponent(phoneNumber)}`);\n    if (params.length > 0) url += `?${params.join('&')}`;\n    return get<Customer[]>(url);\n  },\n\n  // Lấy thanh toán theo khách hàng\n  getPaymentsByCustomerId: async (customerId: number): Promise<CustomerPayment[]> => {\n    return get<CustomerPayment[]>(`${BASE_URL}/customer/${customerId}`);\n  },\n\n  // Lấy thanh toán theo hợp đồng\n  getPaymentsByContractId: async (contractId: number): Promise<CustomerPayment[]> => {\n    return get<CustomerPayment[]>(`${BASE_URL}/contract/${contractId}`);\n  },\n\n  // Lấy hợp đồng đang hoạt động của khách hàng\n  getActiveContractsByCustomerId: async (customerId: number): Promise<CustomerContract[]> => {\n    return get<CustomerContract[]>(`${BASE_URL}/customer/${customerId}/active-contracts`);\n  },\n\n  // Lấy thông tin thanh toán của hợp đồng\n  getContractPaymentInfo: async (contractId: number): Promise<CustomerContract> => {\n    return get<CustomerContract>(`${BASE_URL}/contract/${contractId}/payment-info`);\n  },\n\n  // Lấy tổng số tiền đã thanh toán của hợp đồng\n  getTotalPaidAmountByContractId: async (contractId: number): Promise<number> => {\n    return get<number>(`${BASE_URL}/contract/${contractId}/total-paid`);\n  },\n\n  // Lấy số tiền còn lại của hợp đồng\n  getRemainingAmountByContractId: async (contractId: number): Promise<number> => {\n    return get<number>(`${BASE_URL}/contract/${contractId}/remaining-amount`);\n  },\n\n  // API mới - Tạo thanh toán cho nhiều hợp đồng\n  createPaymentWithMultipleContracts: async (request: CreatePaymentRequest): Promise<CustomerPayment> => {\n    console.log('🚀 Payment service: Creating payment with multiple contracts...', {\n      customerId: request.customerId,\n      totalAmount: request.totalAmount,\n      contractCount: request.contractPayments.length,\n      contracts: request.contractPayments.map(cp => ({\n        contractId: cp.contractId,\n        amount: cp.allocatedAmount\n      })),\n      fullRequest: request\n    });\n\n    try {\n      const result = await post<CustomerPayment>(`${BASE_URL}/multiple-contracts`, request);\n\n      console.log('✅ Payment service: Payment with multiple contracts created successfully:', {\n        id: result.id,\n        totalAmount: result.paymentAmount,\n        contractPayments: result.contractPayments?.length || 0\n      });\n\n      return result;\n    } catch (error) {\n      console.error('❌ Payment service: Error creating payment with multiple contracts:', error);\n      throw error;\n    }\n  },\n\n  // Lấy danh sách thanh toán theo payment ID\n  getContractPaymentsByPaymentId: async (paymentId: number): Promise<ContractPayment[]> => {\n    return get<ContractPayment[]>(`${BASE_URL}/payment/${paymentId}/contract-payments`);\n  },\n\n  // Lấy danh sách thanh toán theo contract ID\n  getContractPaymentsByContractId: async (contractId: number): Promise<ContractPayment[]> => {\n    return get<ContractPayment[]>(`${BASE_URL}/contract/${contractId}/contract-payments`);\n  },\n};\n"], "mappings": "AACA,SAASA,GAAG,EAAEC,IAAI,QAAQ,kBAAkB;AAE5C,MAAMC,QAAQ,GAAG,uBAAuB;AAExC,OAAO,MAAMC,sBAAsB,GAAG;EACpC;EACAC,cAAc,EAAE,MAAAA,CAAA,KAAwC;IACtD,OAAOJ,GAAG,CAAoBE,QAAQ,CAAC;EACzC,CAAC;EAED;EACAG,cAAc,EAAE,MAAOC,EAAU,IAA+B;IAC9D,OAAON,GAAG,CAAkB,GAAGE,QAAQ,YAAYI,EAAE,EAAE,CAAC;EAC1D,CAAC;EAED;EACAC,aAAa,EAAE,MAAOC,OAAwB,IAA+B;IAC3EC,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE;MACrDC,UAAU,EAAEH,OAAO,CAACI,kBAAkB;MACtCC,UAAU,EAAEL,OAAO,CAACK,UAAU;MAC9BC,MAAM,EAAEN,OAAO,CAACO,aAAa;MAC7BC,MAAM,EAAER,OAAO,CAACS,aAAa;MAC7BC,WAAW,EAAEV,OAAO,CAACU;IACvB,CAAC,CAAC;IAEF,MAAMC,MAAM,GAAG,MAAMlB,IAAI,CAAkBC,QAAQ,EAAEM,OAAO,CAAC;IAE7DC,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAE;MAC9DJ,EAAE,EAAEa,MAAM,CAACb,EAAE;MACbQ,MAAM,EAAEK,MAAM,CAACJ,aAAa;MAC5BJ,UAAU,EAAEQ,MAAM,CAACP;IACrB,CAAC,CAAC;IAEF,OAAOO,MAAM;EACf,CAAC;EAED;EACAC,eAAe,EAAE,MAAAA,CAAOC,QAAiB,EAAEC,WAAoB,KAA0B;IACvF,IAAIC,GAAG,GAAG,GAAGrB,QAAQ,kBAAkB;IACvC,MAAMsB,MAAM,GAAG,EAAE;IACjB,IAAIH,QAAQ,EAAEG,MAAM,CAACC,IAAI,CAAC,YAAYC,kBAAkB,CAACL,QAAQ,CAAC,EAAE,CAAC;IACrE,IAAIC,WAAW,EAAEE,MAAM,CAACC,IAAI,CAAC,eAAeC,kBAAkB,CAACJ,WAAW,CAAC,EAAE,CAAC;IAC9E,IAAIE,MAAM,CAACG,MAAM,GAAG,CAAC,EAAEJ,GAAG,IAAI,IAAIC,MAAM,CAACI,IAAI,CAAC,GAAG,CAAC,EAAE;IACpD,OAAO5B,GAAG,CAAauB,GAAG,CAAC;EAC7B,CAAC;EAED;EACAM,uBAAuB,EAAE,MAAOhB,UAAkB,IAAiC;IACjF,OAAOb,GAAG,CAAoB,GAAGE,QAAQ,aAAaW,UAAU,EAAE,CAAC;EACrE,CAAC;EAED;EACAiB,uBAAuB,EAAE,MAAOnB,UAAkB,IAAiC;IACjF,OAAOX,GAAG,CAAoB,GAAGE,QAAQ,aAAaS,UAAU,EAAE,CAAC;EACrE,CAAC;EAED;EACAoB,8BAA8B,EAAE,MAAOlB,UAAkB,IAAkC;IACzF,OAAOb,GAAG,CAAqB,GAAGE,QAAQ,aAAaW,UAAU,mBAAmB,CAAC;EACvF,CAAC;EAED;EACAmB,sBAAsB,EAAE,MAAOrB,UAAkB,IAAgC;IAC/E,OAAOX,GAAG,CAAmB,GAAGE,QAAQ,aAAaS,UAAU,eAAe,CAAC;EACjF,CAAC;EAED;EACAsB,8BAA8B,EAAE,MAAOtB,UAAkB,IAAsB;IAC7E,OAAOX,GAAG,CAAS,GAAGE,QAAQ,aAAaS,UAAU,aAAa,CAAC;EACrE,CAAC;EAED;EACAuB,8BAA8B,EAAE,MAAOvB,UAAkB,IAAsB;IAC7E,OAAOX,GAAG,CAAS,GAAGE,QAAQ,aAAaS,UAAU,mBAAmB,CAAC;EAC3E,CAAC;EAED;EACAwB,kCAAkC,EAAE,MAAOC,OAA6B,IAA+B;IACrG3B,OAAO,CAACC,GAAG,CAAC,iEAAiE,EAAE;MAC7EG,UAAU,EAAEuB,OAAO,CAACvB,UAAU;MAC9BwB,WAAW,EAAED,OAAO,CAACC,WAAW;MAChCC,aAAa,EAAEF,OAAO,CAACG,gBAAgB,CAACZ,MAAM;MAC9Ca,SAAS,EAAEJ,OAAO,CAACG,gBAAgB,CAACE,GAAG,CAACC,EAAE,KAAK;QAC7C/B,UAAU,EAAE+B,EAAE,CAAC/B,UAAU;QACzBG,MAAM,EAAE4B,EAAE,CAACC;MACb,CAAC,CAAC,CAAC;MACHC,WAAW,EAAER;IACf,CAAC,CAAC;IAEF,IAAI;MAAA,IAAAS,qBAAA;MACF,MAAM1B,MAAM,GAAG,MAAMlB,IAAI,CAAkB,GAAGC,QAAQ,qBAAqB,EAAEkC,OAAO,CAAC;MAErF3B,OAAO,CAACC,GAAG,CAAC,0EAA0E,EAAE;QACtFJ,EAAE,EAAEa,MAAM,CAACb,EAAE;QACb+B,WAAW,EAAElB,MAAM,CAACJ,aAAa;QACjCwB,gBAAgB,EAAE,EAAAM,qBAAA,GAAA1B,MAAM,CAACoB,gBAAgB,cAAAM,qBAAA,uBAAvBA,qBAAA,CAAyBlB,MAAM,KAAI;MACvD,CAAC,CAAC;MAEF,OAAOR,MAAM;IACf,CAAC,CAAC,OAAO2B,KAAK,EAAE;MACdrC,OAAO,CAACqC,KAAK,CAAC,oEAAoE,EAAEA,KAAK,CAAC;MAC1F,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAC,8BAA8B,EAAE,MAAOC,SAAiB,IAAiC;IACvF,OAAOhD,GAAG,CAAoB,GAAGE,QAAQ,YAAY8C,SAAS,oBAAoB,CAAC;EACrF,CAAC;EAED;EACAC,+BAA+B,EAAE,MAAOtC,UAAkB,IAAiC;IACzF,OAAOX,GAAG,CAAoB,GAAGE,QAAQ,aAAaS,UAAU,oBAAoB,CAAC;EACvF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}