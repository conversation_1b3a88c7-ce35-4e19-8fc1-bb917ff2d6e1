{"ast": null, "code": "var _jsxFileName = \"D:\\\\HeThongCongTyQuanLyNhanCong\\\\Microservice_With_Kubernetes\\\\microservice_fe\\\\src\\\\components\\\\common\\\\ContractSuccessAlert.tsx\";\nimport React from 'react';\nimport { Alert, AlertTitle, Box, Button, Typography, Divider, Chip } from '@mui/material';\nimport { CheckCircle as CheckCircleIcon, Visibility as VisibilityIcon, List as ListIcon, Add as AddIcon } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ContractSuccessAlert = ({\n  contract,\n  onViewDetails,\n  onViewList,\n  onCreateNew,\n  redirectCountdown\n}) => {\n  var _contract$totalAmount;\n  return /*#__PURE__*/_jsxDEV(Alert, {\n    severity: \"success\",\n    icon: /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n      fontSize: \"large\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 13\n    }, this),\n    sx: {\n      mb: 3,\n      '& .MuiAlert-message': {\n        width: '100%'\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(AlertTitle, {\n      sx: {\n        fontSize: '1.2rem',\n        fontWeight: 'bold'\n      },\n      children: \"\\uD83C\\uDF89 T\\u1EA1o h\\u1EE3p \\u0111\\u1ED3ng th\\xE0nh c\\xF4ng!\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        sx: {\n          mb: 2\n        },\n        children: [\"H\\u1EE3p \\u0111\\u1ED3ng \", /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: [\"#\", contract.id]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 20\n        }, this), \" \\u0111\\xE3 \\u0111\\u01B0\\u1EE3c t\\u1EA1o th\\xE0nh c\\xF4ng v\\u1EDBi t\\u1ED5ng gi\\xE1 tr\\u1ECB\", ' ', /*#__PURE__*/_jsxDEV(Chip, {\n          label: `${(_contract$totalAmount = contract.totalAmount) === null || _contract$totalAmount === void 0 ? void 0 : _contract$totalAmount.toLocaleString('vi-VN')} VNĐ`,\n          color: \"success\",\n          variant: \"outlined\",\n          size: \"small\",\n          sx: {\n            fontWeight: 'bold'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), redirectCountdown !== null && redirectCountdown !== undefined && redirectCountdown > 0 ? /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"primary\",\n        sx: {\n          mb: 2,\n          fontWeight: 'bold'\n        },\n        children: [\"\\uD83D\\uDD04 T\\u1EF1 \\u0111\\u1ED9ng chuy\\u1EC3n \\u0111\\u1EBFn trang chi ti\\u1EBFt h\\u1EE3p \\u0111\\u1ED3ng trong \", redirectCountdown, \" gi\\xE2y...\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        sx: {\n          mb: 2\n        },\n        children: \"B\\u1EA1n mu\\u1ED1n l\\xE0m g\\xEC ti\\u1EBFp theo?\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 1,\n          flexWrap: 'wrap'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 24\n          }, this),\n          onClick: onViewDetails,\n          size: \"small\",\n          children: \"Xem chi ti\\u1EBFt h\\u1EE3p \\u0111\\u1ED3ng\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(ListIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 24\n          }, this),\n          onClick: onViewList,\n          size: \"small\",\n          children: \"Danh s\\xE1ch h\\u1EE3p \\u0111\\u1ED3ng\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 24\n          }, this),\n          onClick: onCreateNew,\n          size: \"small\",\n          children: \"T\\u1EA1o h\\u1EE3p \\u0111\\u1ED3ng m\\u1EDBi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n};\n_c = ContractSuccessAlert;\nexport default ContractSuccessAlert;\nvar _c;\n$RefreshReg$(_c, \"ContractSuccessAlert\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Box", "<PERSON><PERSON>", "Typography", "Divider", "Chip", "CheckCircle", "CheckCircleIcon", "Visibility", "VisibilityIcon", "List", "ListIcon", "Add", "AddIcon", "jsxDEV", "_jsxDEV", "ContractSuccessAlert", "contract", "onViewDetails", "onViewList", "onCreateNew", "redirectCountdown", "_contract$totalAmount", "severity", "icon", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "mb", "width", "children", "fontWeight", "mt", "variant", "id", "label", "totalAmount", "toLocaleString", "color", "size", "my", "undefined", "display", "gap", "flexWrap", "startIcon", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/common/ContractSuccessAlert.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Al<PERSON>,\n  AlertTitle,\n  <PERSON>,\n  Button,\n  Typography,\n  Divider,\n  Chip\n} from '@mui/material';\nimport {\n  CheckCircle as CheckCircleIcon,\n  Visibility as VisibilityIcon,\n  List as ListIcon,\n  Add as AddIcon\n} from '@mui/icons-material';\nimport { CustomerContract } from '../../models';\n\ninterface ContractSuccessAlertProps {\n  contract: CustomerContract;\n  onViewDetails: () => void;\n  onViewList: () => void;\n  onCreateNew: () => void;\n  redirectCountdown?: number | null;\n}\n\nconst ContractSuccessAlert: React.FC<ContractSuccessAlertProps> = ({\n  contract,\n  onViewDetails,\n  onViewList,\n  onCreateNew,\n  redirectCountdown\n}) => {\n  return (\n    <Alert\n      severity=\"success\"\n      icon={<CheckCircleIcon fontSize=\"large\" />}\n      sx={{\n        mb: 3,\n        '& .MuiAlert-message': {\n          width: '100%'\n        }\n      }}\n    >\n      <AlertTitle sx={{ fontSize: '1.2rem', fontWeight: 'bold' }}>\n        🎉 Tạo hợp đồng thành công!\n      </AlertTitle>\n\n      <Box sx={{ mt: 2 }}>\n        <Typography variant=\"body1\" sx={{ mb: 2 }}>\n          Hợp đồng <strong>#{contract.id}</strong> đã được tạo thành công với tổng giá trị{' '}\n          <Chip\n            label={`${contract.totalAmount?.toLocaleString('vi-VN')} VNĐ`}\n            color=\"success\"\n            variant=\"outlined\"\n            size=\"small\"\n            sx={{ fontWeight: 'bold' }}\n          />\n        </Typography>\n\n        <Divider sx={{ my: 2 }} />\n\n        {redirectCountdown !== null && redirectCountdown !== undefined && redirectCountdown > 0 ? (\n          <Typography variant=\"body2\" color=\"primary\" sx={{ mb: 2, fontWeight: 'bold' }}>\n            🔄 Tự động chuyển đến trang chi tiết hợp đồng trong {redirectCountdown} giây...\n          </Typography>\n        ) : (\n          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n            Bạn muốn làm gì tiếp theo?\n          </Typography>\n        )}\n\n        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>\n          <Button\n            variant=\"contained\"\n            startIcon={<VisibilityIcon />}\n            onClick={onViewDetails}\n            size=\"small\"\n          >\n            Xem chi tiết hợp đồng\n          </Button>\n\n          <Button\n            variant=\"outlined\"\n            startIcon={<ListIcon />}\n            onClick={onViewList}\n            size=\"small\"\n          >\n            Danh sách hợp đồng\n          </Button>\n\n          <Button\n            variant=\"outlined\"\n            startIcon={<AddIcon />}\n            onClick={onCreateNew}\n            size=\"small\"\n          >\n            Tạo hợp đồng mới\n          </Button>\n        </Box>\n      </Box>\n    </Alert>\n  );\n};\n\nexport default ContractSuccessAlert;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,MAAM,EACNC,UAAU,EACVC,OAAO,EACPC,IAAI,QACC,eAAe;AACtB,SACEC,WAAW,IAAIC,eAAe,EAC9BC,UAAU,IAAIC,cAAc,EAC5BC,IAAI,IAAIC,QAAQ,EAChBC,GAAG,IAAIC,OAAO,QACT,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAW7B,MAAMC,oBAAyD,GAAGA,CAAC;EACjEC,QAAQ;EACRC,aAAa;EACbC,UAAU;EACVC,WAAW;EACXC;AACF,CAAC,KAAK;EAAA,IAAAC,qBAAA;EACJ,oBACEP,OAAA,CAAChB,KAAK;IACJwB,QAAQ,EAAC,SAAS;IAClBC,IAAI,eAAET,OAAA,CAACR,eAAe;MAACkB,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAE;IAC3CC,EAAE,EAAE;MACFC,EAAE,EAAE,CAAC;MACL,qBAAqB,EAAE;QACrBC,KAAK,EAAE;MACT;IACF,CAAE;IAAAC,QAAA,gBAEFlB,OAAA,CAACf,UAAU;MAAC8B,EAAE,EAAE;QAAEL,QAAQ,EAAE,QAAQ;QAAES,UAAU,EAAE;MAAO,CAAE;MAAAD,QAAA,EAAC;IAE5D;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbd,OAAA,CAACd,GAAG;MAAC6B,EAAE,EAAE;QAAEK,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,gBACjBlB,OAAA,CAACZ,UAAU;QAACiC,OAAO,EAAC,OAAO;QAACN,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAE,QAAA,GAAC,0BAChC,eAAAlB,OAAA;UAAAkB,QAAA,GAAQ,GAAC,EAAChB,QAAQ,CAACoB,EAAE;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,gGAAwC,EAAC,GAAG,eACpFd,OAAA,CAACV,IAAI;UACHiC,KAAK,EAAE,IAAAhB,qBAAA,GAAGL,QAAQ,CAACsB,WAAW,cAAAjB,qBAAA,uBAApBA,qBAAA,CAAsBkB,cAAc,CAAC,OAAO,CAAC,MAAO;UAC9DC,KAAK,EAAC,SAAS;UACfL,OAAO,EAAC,UAAU;UAClBM,IAAI,EAAC,OAAO;UACZZ,EAAE,EAAE;YAAEI,UAAU,EAAE;UAAO;QAAE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC,eAEbd,OAAA,CAACX,OAAO;QAAC0B,EAAE,EAAE;UAAEa,EAAE,EAAE;QAAE;MAAE;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAEzBR,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAKuB,SAAS,IAAIvB,iBAAiB,GAAG,CAAC,gBACrFN,OAAA,CAACZ,UAAU;QAACiC,OAAO,EAAC,OAAO;QAACK,KAAK,EAAC,SAAS;QAACX,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEG,UAAU,EAAE;QAAO,CAAE;QAAAD,QAAA,GAAC,kHACzB,EAACZ,iBAAiB,EAAC,aACzE;MAAA;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,gBAEbd,OAAA,CAACZ,UAAU;QAACiC,OAAO,EAAC,OAAO;QAACK,KAAK,EAAC,gBAAgB;QAACX,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAE,QAAA,EAAC;MAElE;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CACb,eAEDd,OAAA,CAACd,GAAG;QAAC6B,EAAE,EAAE;UAAEe,OAAO,EAAE,MAAM;UAAEC,GAAG,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAd,QAAA,gBACrDlB,OAAA,CAACb,MAAM;UACLkC,OAAO,EAAC,WAAW;UACnBY,SAAS,eAAEjC,OAAA,CAACN,cAAc;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC9BoB,OAAO,EAAE/B,aAAc;UACvBwB,IAAI,EAAC,OAAO;UAAAT,QAAA,EACb;QAED;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETd,OAAA,CAACb,MAAM;UACLkC,OAAO,EAAC,UAAU;UAClBY,SAAS,eAAEjC,OAAA,CAACJ,QAAQ;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBoB,OAAO,EAAE9B,UAAW;UACpBuB,IAAI,EAAC,OAAO;UAAAT,QAAA,EACb;QAED;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETd,OAAA,CAACb,MAAM;UACLkC,OAAO,EAAC,UAAU;UAClBY,SAAS,eAAEjC,OAAA,CAACF,OAAO;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBoB,OAAO,EAAE7B,WAAY;UACrBsB,IAAI,EAAC,OAAO;UAAAT,QAAA,EACb;QAED;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEZ,CAAC;AAACqB,EAAA,GA7EIlC,oBAAyD;AA+E/D,eAAeA,oBAAoB;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}