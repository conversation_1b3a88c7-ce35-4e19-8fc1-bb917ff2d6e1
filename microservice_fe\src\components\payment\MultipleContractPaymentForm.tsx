import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  Alert,
  Divider,
  Grid,
  InputAdornment
} from '@mui/material';

import CloseIcon from '@mui/icons-material/Close';
import PaymentIcon from '@mui/icons-material/Payment';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';

import {
  CustomerContract,
  CreatePaymentRequest,
  ContractPaymentDto,
  PaymentMethodMap
} from '../../models';
import { formatCurrency } from '../../utils/formatters';
import { formatDateLocalized } from '../../utils/dateUtils';

interface MultipleContractPaymentFormProps {
  open: boolean;
  contracts: CustomerContract[];
  selectedContracts?: CustomerContract[];
  customerId: number;
  customerName: string;
  onClose: () => void;
  onSubmit: (request: CreatePaymentRequest) => void;
  loading?: boolean;
}

const MultipleContractPaymentForm: React.FC<MultipleContractPaymentFormProps> = ({
  open,
  contracts,
  selectedContracts,
  customerId,
  customerName,
  onClose,
  onSubmit,
  loading = false,
}) => {
  const [paymentMethod, setPaymentMethod] = useState<number>(0);
  const [note, setNote] = useState<string>('');
  const [contractPayments, setContractPayments] = useState<ContractPaymentDto[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (open) {
      // Use selectedContracts if provided, otherwise use all contracts
      const contractsToUse = selectedContracts && selectedContracts.length > 0 ? selectedContracts : contracts;

      if (contractsToUse.length > 0) {
        // Initialize with selected contracts and their remaining amounts
        const initialPayments: ContractPaymentDto[] = contractsToUse
          .filter(contract => (contract.totalAmount! - (contract.totalPaid || 0)) > 0) // Only unpaid contracts
          .map(contract => ({
            contractId: contract.id!,
            allocatedAmount: contract.totalAmount! - (contract.totalPaid || 0),
            contractDescription: contract.description,
            contractTotalAmount: contract.totalAmount,
            contractTotalPaid: contract.totalPaid || 0,
            contractRemainingAmount: contract.totalAmount! - (contract.totalPaid || 0)
          }));

        setContractPayments(initialPayments);
        setPaymentMethod(0);
        setNote('');
        setError(null);
      }
    }
  }, [open, contracts, selectedContracts]);

  const handleAmountChange = (contractId: number, amount: number) => {
    setContractPayments(prev =>
      prev.map(cp =>
        cp.contractId === contractId
          ? { ...cp, allocatedAmount: amount }
          : cp
      )
    );
    setError(null);
  };

  const handleRemoveContract = (contractId: number) => {
    setContractPayments(prev => prev.filter(cp => cp.contractId !== contractId));
  };

  const handleAddContract = (contract: CustomerContract) => {
    const exists = contractPayments.some(cp => cp.contractId === contract.id);
    if (!exists) {
      const newPayment: ContractPaymentDto = {
        contractId: contract.id!,
        allocatedAmount: contract.totalAmount! - (contract.totalPaid || 0),
        contractDescription: contract.description,
        contractTotalAmount: contract.totalAmount,
        contractTotalPaid: contract.totalPaid || 0,
        contractRemainingAmount: contract.totalAmount! - (contract.totalPaid || 0)
      };
      setContractPayments(prev => [...prev, newPayment]);
    }
  };

  const getTotalAmount = () => {
    return contractPayments.reduce((sum, cp) => sum + cp.allocatedAmount, 0);
  };

  const validateForm = (): string | null => {
    if (contractPayments.length === 0) {
      return 'Phải chọn ít nhất một hợp đồng để thanh toán';
    }

    for (const cp of contractPayments) {
      if (cp.allocatedAmount <= 0) {
        return 'Số tiền thanh toán phải lớn hơn 0';
      }
      if (cp.allocatedAmount > cp.contractRemainingAmount!) {
        return `Số tiền thanh toán cho hợp đồng ${cp.contractId} không được vượt quá số tiền còn lại`;
      }
    }

    return null;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    const request: CreatePaymentRequest = {
      paymentDate: new Date().toISOString(),
      paymentMethod,
      totalAmount: getTotalAmount(),
      note: note || undefined,
      customerId,
      contractPayments: contractPayments.map(cp => ({
        contractId: cp.contractId,
        allocatedAmount: cp.allocatedAmount
      }))
    };

    onSubmit(request);
  };

  const availableContracts = contracts.filter(contract =>
    !contractPayments.some(cp => cp.contractId === contract.id)
  );

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: { minHeight: '70vh' }
      }}
    >
      <DialogTitle sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        pb: 1
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <PaymentIcon color="primary" />
          <Typography variant="h6">
            Thanh toán nhiều hợp đồng
          </Typography>
        </Box>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ px: 3 }}>
        {/* Customer Info */}
        <Box sx={{ mb: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
          <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
            Thông tin khách hàng
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {customerName} (ID: {customerId})
          </Typography>
        </Box>

        <form id="multiple-payment-form" onSubmit={handleSubmit}>
          {/* Payment Method */}
          <Box sx={{ display: 'flex', gap: 3, mb: 3, flexDirection: { xs: 'column', md: 'row' } }}>
            <Box sx={{ flex: 1 }}>
              <FormControl fullWidth required>
                <InputLabel>Phương thức thanh toán</InputLabel>
                <Select
                  value={paymentMethod}
                  onChange={(e) => setPaymentMethod(Number(e.target.value))}
                  label="Phương thức thanh toán"
                >
                  {Object.entries(PaymentMethodMap).map(([key, value]) => (
                    <MenuItem key={key} value={Number(key)}>
                      {value}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
            <Box sx={{ flex: 1 }}>
              <TextField
                fullWidth
                label="Ghi chú"
                value={note}
                onChange={(e) => setNote(e.target.value)}
                multiline
                rows={2}
                placeholder="Nhập ghi chú cho thanh toán..."
              />
            </Box>
          </Box>

          {/* Contract Payments Table */}
          <Typography variant="h6" gutterBottom sx={{ mt: 3, mb: 2 }}>
            Danh sách hợp đồng thanh toán
          </Typography>

          {contractPayments.length > 0 ? (
            <TableContainer component={Paper} sx={{ mb: 3 }}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Hợp đồng</TableCell>
                    <TableCell align="right">Tổng tiền</TableCell>
                    <TableCell align="right">Đã thanh toán</TableCell>
                    <TableCell align="right">Còn lại</TableCell>
                    <TableCell align="right">Thanh toán</TableCell>
                    <TableCell align="center">Thao tác</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {contractPayments.map((cp) => (
                    <TableRow key={cp.contractId}>
                      <TableCell>
                        <Typography variant="body2" fontWeight="bold">
                          #{cp.contractId}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {cp.contractDescription}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        {formatCurrency(cp.contractTotalAmount!)}
                      </TableCell>
                      <TableCell align="right">
                        {formatCurrency(cp.contractTotalPaid!)}
                      </TableCell>
                      <TableCell align="right">
                        <Typography color="primary" fontWeight="bold">
                          {formatCurrency(cp.contractRemainingAmount!)}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <TextField
                          type="number"
                          size="small"
                          value={cp.allocatedAmount}
                          onChange={(e) => handleAmountChange(cp.contractId, Number(e.target.value))}
                          InputProps={{
                            endAdornment: <InputAdornment position="end">VNĐ</InputAdornment>,
                          }}
                          sx={{ width: 150 }}
                          inputProps={{ min: 0, max: cp.contractRemainingAmount }}
                        />
                      </TableCell>
                      <TableCell align="center">
                        <IconButton
                          onClick={() => handleRemoveContract(cp.contractId)}
                          color="error"
                          size="small"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Alert severity="info" sx={{ mb: 3 }}>
              Chưa có hợp đồng nào được chọn để thanh toán
            </Alert>
          )}

          {/* Add Contract Section */}
          {availableContracts.length > 0 && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle1" gutterBottom>
                Thêm hợp đồng khác
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {availableContracts.map((contract) => (
                  <Chip
                    key={contract.id}
                    label={`#${contract.id} - ${formatCurrency(contract.totalAmount! - (contract.totalPaid || 0))}`}
                    onClick={() => handleAddContract(contract)}
                    icon={<AddIcon />}
                    variant="outlined"
                    clickable
                  />
                ))}
              </Box>
            </Box>
          )}

          {/* Total Amount */}
          <Divider sx={{ my: 2 }} />
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              Tổng số tiền thanh toán:
            </Typography>
            <Typography variant="h5" color="primary" fontWeight="bold">
              {formatCurrency(getTotalAmount())}
            </Typography>
          </Box>

          {/* Error Message */}
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
        </form>
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 2, justifyContent: 'space-between' }}>
        <Button
          onClick={onClose}
          color="inherit"
          variant="outlined"
          startIcon={<CloseIcon />}
        >
          Hủy
        </Button>
        <Button
          type="submit"
          form="multiple-payment-form"
          variant="contained"
          color="primary"
          startIcon={<PaymentIcon />}
          size="large"
          disabled={loading || contractPayments.length === 0 || getTotalAmount() <= 0}
        >
          {loading ? 'Đang xử lý...' : `Thanh toán ${formatCurrency(getTotalAmount())}`}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default MultipleContractPaymentForm;
