{"ast": null, "code": "var _jsxFileName = \"D:\\\\HeThongCongTyQuanLyNhanCong\\\\Microservice_With_Kubernetes\\\\microservice_fe\\\\src\\\\pages\\\\CreateContractPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Box } from '@mui/material';\nimport { CustomerContractForm } from '../components/contract';\nimport { contractService } from '../services/contract/contractService';\nimport { LoadingSpinner, ErrorAlert, SuccessAlert, ContractSuccessAlert } from '../components/common';\nimport { calculateContractAmount } from '../utils/contractCalculationUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CreateContractPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const [contract, setContract] = useState({\n    customerId: 0,\n    startingDate: '',\n    endingDate: '',\n    totalAmount: 0,\n    description: '',\n    jobDetails: [],\n    status: 0 // Pending\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n  const [createdContract, setCreatedContract] = useState(null);\n  const [redirectCountdown, setRedirectCountdown] = useState(null);\n\n  // Auto-redirect to contract details after successful creation\n  useEffect(() => {\n    if (createdContract !== null && createdContract !== void 0 && createdContract.id && redirectCountdown === null) {\n      // Start countdown from 3 seconds\n      setRedirectCountdown(3);\n    }\n  }, [createdContract, redirectCountdown]);\n\n  // Countdown timer for auto-redirect\n  useEffect(() => {\n    if (redirectCountdown !== null && redirectCountdown > 0) {\n      const timer = setTimeout(() => {\n        setRedirectCountdown(redirectCountdown - 1);\n      }, 1000);\n      return () => clearTimeout(timer);\n    } else if (redirectCountdown === 0 && createdContract !== null && createdContract !== void 0 && createdContract.id) {\n      // Auto-redirect when countdown reaches 0\n      navigate(`/contracts/${createdContract.id}`);\n    }\n  }, [redirectCountdown, createdContract, navigate]);\n  const handleContractChange = updatedContract => {\n    setContract(updatedContract);\n  };\n  const handleViewDetails = () => {\n    if (createdContract !== null && createdContract !== void 0 && createdContract.id) {\n      navigate(`/contracts/${createdContract.id}`);\n    }\n  };\n  const handleViewList = () => {\n    navigate('/contracts');\n  };\n  const handleCreateNew = () => {\n    // Reset form state\n    setContract({\n      customerId: 0,\n      startingDate: '',\n      endingDate: '',\n      totalAmount: 0,\n      description: '',\n      jobDetails: [],\n      status: 0\n    });\n    setCreatedContract(null);\n    setSuccess(null);\n    setError(null);\n    setRedirectCountdown(null);\n  };\n  const validateContract = () => {\n    if (!contract.customerId || contract.customerId === 0) {\n      setError('Vui lòng chọn khách hàng');\n      return false;\n    }\n\n    // Contract dates are auto-calculated from job details, no need to validate manually\n    // Contract address is auto-derived from job details, no need to validate manually\n    // Total amount is auto-calculated, no need to validate manually\n\n    if (!contract.jobDetails || contract.jobDetails.length === 0) {\n      setError('Vui lòng thêm ít nhất một chi tiết công việc');\n      return false;\n    }\n\n    // Validate each job detail\n    for (const jobDetail of contract.jobDetails) {\n      if (!jobDetail.jobCategoryId || jobDetail.jobCategoryId === 0) {\n        setError('Vui lòng chọn loại công việc cho tất cả chi tiết công việc');\n        return false;\n      }\n      if (!jobDetail.startDate) {\n        setError('Vui lòng nhập ngày bắt đầu cho tất cả chi tiết công việc');\n        return false;\n      }\n      if (!jobDetail.endDate) {\n        setError('Vui lòng nhập ngày kết thúc cho tất cả chi tiết công việc');\n        return false;\n      }\n\n      // Work location is optional - will be auto-assigned if not provided\n      // if (!jobDetail.workLocation) {\n      //   setError('Vui lòng nhập địa điểm làm việc cho tất cả chi tiết công việc');\n      //   return false;\n      // }\n\n      if (!jobDetail.workShifts || jobDetail.workShifts.length === 0) {\n        setError('Vui lòng thêm ít nhất một ca làm việc cho mỗi chi tiết công việc');\n        return false;\n      }\n\n      // Validate each work shift\n      for (const workShift of jobDetail.workShifts) {\n        if (!workShift.startTime) {\n          setError('Vui lòng nhập giờ bắt đầu cho tất cả ca làm việc');\n          return false;\n        }\n        if (!workShift.endTime) {\n          setError('Vui lòng nhập giờ kết thúc cho tất cả ca làm việc');\n          return false;\n        }\n        if (!workShift.numberOfWorkers || workShift.numberOfWorkers <= 0) {\n          setError('Vui lòng nhập số lượng người lao động hợp lệ cho tất cả ca làm việc');\n          return false;\n        }\n        if (workShift.salary === undefined || workShift.salary < 0) {\n          setError('Vui lòng nhập mức lương hợp lệ cho tất cả ca làm việc');\n          return false;\n        }\n        if (!workShift.workingDays) {\n          setError('Vui lòng chọn ngày làm việc cho tất cả ca làm việc');\n          return false;\n        }\n      }\n    }\n    return true;\n  };\n  const handleSubmit = async () => {\n    // Prevent double submission with multiple checks\n    if (loading) {\n      console.log('Contract submission blocked: already loading');\n      return;\n    }\n\n    // Enhanced duplicate prevention\n    const now = Date.now();\n    const lastSubmission = localStorage.getItem('lastContractSubmission');\n    const submissionKey = `contract_${contract.customerId}_${contract.startingDate}_${contract.endingDate}_${Math.round(contract.totalAmount || 0)}`;\n    const lastSubmissionKey = localStorage.getItem('lastContractSubmissionKey');\n\n    // Prevent rapid successive submissions\n    if (lastSubmission && now - parseInt(lastSubmission) < 2000) {\n      console.log('Contract submission blocked: too rapid (within 2 seconds)');\n      setError('Vui lòng đợi ít nhất 2 giây trước khi gửi lại');\n      return;\n    }\n\n    // Prevent duplicate contract submissions\n    if (lastSubmissionKey === submissionKey && lastSubmission && now - parseInt(lastSubmission) < 60000) {\n      console.log('Contract submission blocked: duplicate contract detected');\n      setError('Hợp đồng tương tự đã được gửi gần đây. Vui lòng kiểm tra lại.');\n      return;\n    }\n    setError(null);\n    if (!validateContract()) {\n      return;\n    }\n\n    // Ensure total amount is calculated before submitting\n    const calculation = calculateContractAmount(contract);\n    const contractToSubmit = {\n      ...contract,\n      totalAmount: calculation.totalAmount\n    };\n\n    // Mark submission time and key to prevent rapid resubmission and duplicates\n    localStorage.setItem('lastContractSubmission', now.toString());\n    localStorage.setItem('lastContractSubmissionKey', submissionKey);\n    setLoading(true);\n    try {\n      console.log('🚀 Submitting contract creation request...', contractToSubmit);\n\n      // Clear any previous error state\n      setError(null);\n      const createdContract = await contractService.createContract(contractToSubmit);\n      console.log('✅ Contract created successfully:', {\n        id: createdContract.id,\n        totalAmount: createdContract.totalAmount,\n        customerId: createdContract.customerId\n      });\n\n      // Verify the contract was actually created with valid data\n      if (!createdContract || !createdContract.id) {\n        throw new Error('Hợp đồng được tạo nhưng không nhận được thông tin hợp lệ từ máy chủ');\n      }\n\n      // Store the created contract for display\n      setCreatedContract(createdContract);\n      setSuccess(`Hợp đồng #${createdContract.id} đã được tạo thành công!`);\n\n      // Clear the submission timestamp and key on success\n      localStorage.removeItem('lastContractSubmission');\n      localStorage.removeItem('lastContractSubmissionKey');\n\n      // Set flag to trigger refresh in contracts list\n      localStorage.setItem('contractsListNeedsRefresh', 'true');\n    } catch (err) {\n      var _err$response, _err$response2;\n      console.error('❌ Contract creation failed:', err);\n\n      // Provide more specific error messages\n      let errorMessage = 'Đã xảy ra lỗi khi tạo hợp đồng';\n      if (((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status) === 400) {\n        errorMessage = 'Dữ liệu hợp đồng không hợp lệ. Vui lòng kiểm tra lại thông tin.';\n      } else if (((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.status) === 500) {\n        errorMessage = 'Lỗi máy chủ nội bộ. Vui lòng thử lại sau.';\n      } else if (err.message) {\n        errorMessage = err.message;\n      }\n      setError(errorMessage);\n\n      // Clear the submission timestamp on error to allow retry\n      localStorage.removeItem('lastContractSubmission');\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n      fullScreen: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [error && /*#__PURE__*/_jsxDEV(ErrorAlert, {\n      message: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 256,\n      columnNumber: 17\n    }, this), createdContract && /*#__PURE__*/_jsxDEV(ContractSuccessAlert, {\n      contract: createdContract,\n      onViewDetails: handleViewDetails,\n      onViewList: handleViewList,\n      onCreateNew: handleCreateNew,\n      redirectCountdown: redirectCountdown\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 9\n    }, this), success && !createdContract && /*#__PURE__*/_jsxDEV(SuccessAlert, {\n      message: success\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 39\n    }, this), /*#__PURE__*/_jsxDEV(CustomerContractForm, {\n      contract: contract,\n      onChange: handleContractChange,\n      onSubmit: handleSubmit,\n      loading: loading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 272,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 255,\n    columnNumber: 5\n  }, this);\n};\n_s(CreateContractPage, \"5+19T5LBErczPl/NfoK+3C4aBWw=\", false, function () {\n  return [useNavigate];\n});\n_c = CreateContractPage;\nexport default CreateContractPage;\nvar _c;\n$RefreshReg$(_c, \"CreateContractPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "Box", "CustomerContractForm", "contractService", "LoadingSpinner", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "ContractSuccessAlert", "calculateContractAmount", "jsxDEV", "_jsxDEV", "CreateContractPage", "_s", "navigate", "contract", "setContract", "customerId", "startingDate", "endingDate", "totalAmount", "description", "jobDetails", "status", "loading", "setLoading", "error", "setError", "success", "setSuccess", "createdContract", "setCreatedContract", "redirectCountdown", "setRedirectCountdown", "id", "timer", "setTimeout", "clearTimeout", "handleContractChange", "updatedContract", "handleViewDetails", "handleViewList", "handleCreateNew", "validateContract", "length", "jobDetail", "jobCategoryId", "startDate", "endDate", "workShifts", "workShift", "startTime", "endTime", "numberOfWorkers", "salary", "undefined", "workingDays", "handleSubmit", "console", "log", "now", "Date", "lastSubmission", "localStorage", "getItem", "<PERSON><PERSON><PERSON>", "Math", "round", "lastSubmissionKey", "parseInt", "calculation", "contractToSubmit", "setItem", "toString", "createContract", "Error", "removeItem", "err", "_err$response", "_err$response2", "errorMessage", "response", "message", "fullScreen", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "onViewDetails", "onViewList", "onCreateNew", "onChange", "onSubmit", "_c", "$RefreshReg$"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/pages/CreateContractPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Box } from '@mui/material';\nimport { CustomerContractForm } from '../components/contract';\nimport { CustomerContract } from '../models';\nimport { contractService } from '../services/contract/contractService';\nimport { LoadingSpinner, ErrorAlert, SuccessAlert, ContractSuccessAlert } from '../components/common';\nimport { calculateContractAmount } from '../utils/contractCalculationUtils';\n\nconst CreateContractPage: React.FC = () => {\n  const navigate = useNavigate();\n  const [contract, setContract] = useState<Partial<CustomerContract>>({\n    customerId: 0,\n    startingDate: '',\n    endingDate: '',\n    totalAmount: 0,\n    description: '',\n    jobDetails: [],\n    status: 0 // Pending\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  const [createdContract, setCreatedContract] = useState<CustomerContract | null>(null);\n  const [redirectCountdown, setRedirectCountdown] = useState<number | null>(null);\n\n  // Auto-redirect to contract details after successful creation\n  useEffect(() => {\n    if (createdContract?.id && redirectCountdown === null) {\n      // Start countdown from 3 seconds\n      setRedirectCountdown(3);\n    }\n  }, [createdContract, redirectCountdown]);\n\n  // Countdown timer for auto-redirect\n  useEffect(() => {\n    if (redirectCountdown !== null && redirectCountdown > 0) {\n      const timer = setTimeout(() => {\n        setRedirectCountdown(redirectCountdown - 1);\n      }, 1000);\n      return () => clearTimeout(timer);\n    } else if (redirectCountdown === 0 && createdContract?.id) {\n      // Auto-redirect when countdown reaches 0\n      navigate(`/contracts/${createdContract.id}`);\n    }\n  }, [redirectCountdown, createdContract, navigate]);\n\n  const handleContractChange = (updatedContract: Partial<CustomerContract>) => {\n    setContract(updatedContract);\n  };\n\n  const handleViewDetails = () => {\n    if (createdContract?.id) {\n      navigate(`/contracts/${createdContract.id}`);\n    }\n  };\n\n  const handleViewList = () => {\n    navigate('/contracts');\n  };\n\n  const handleCreateNew = () => {\n    // Reset form state\n    setContract({\n      customerId: 0,\n      startingDate: '',\n      endingDate: '',\n      totalAmount: 0,\n      description: '',\n      jobDetails: [],\n      status: 0\n    });\n    setCreatedContract(null);\n    setSuccess(null);\n    setError(null);\n    setRedirectCountdown(null);\n  };\n\n  const validateContract = (): boolean => {\n    if (!contract.customerId || contract.customerId === 0) {\n      setError('Vui lòng chọn khách hàng');\n      return false;\n    }\n\n    // Contract dates are auto-calculated from job details, no need to validate manually\n    // Contract address is auto-derived from job details, no need to validate manually\n    // Total amount is auto-calculated, no need to validate manually\n\n    if (!contract.jobDetails || contract.jobDetails.length === 0) {\n      setError('Vui lòng thêm ít nhất một chi tiết công việc');\n      return false;\n    }\n\n    // Validate each job detail\n    for (const jobDetail of contract.jobDetails) {\n      if (!jobDetail.jobCategoryId || jobDetail.jobCategoryId === 0) {\n        setError('Vui lòng chọn loại công việc cho tất cả chi tiết công việc');\n        return false;\n      }\n\n      if (!jobDetail.startDate) {\n        setError('Vui lòng nhập ngày bắt đầu cho tất cả chi tiết công việc');\n        return false;\n      }\n\n      if (!jobDetail.endDate) {\n        setError('Vui lòng nhập ngày kết thúc cho tất cả chi tiết công việc');\n        return false;\n      }\n\n      // Work location is optional - will be auto-assigned if not provided\n      // if (!jobDetail.workLocation) {\n      //   setError('Vui lòng nhập địa điểm làm việc cho tất cả chi tiết công việc');\n      //   return false;\n      // }\n\n      if (!jobDetail.workShifts || jobDetail.workShifts.length === 0) {\n        setError('Vui lòng thêm ít nhất một ca làm việc cho mỗi chi tiết công việc');\n        return false;\n      }\n\n      // Validate each work shift\n      for (const workShift of jobDetail.workShifts) {\n        if (!workShift.startTime) {\n          setError('Vui lòng nhập giờ bắt đầu cho tất cả ca làm việc');\n          return false;\n        }\n\n        if (!workShift.endTime) {\n          setError('Vui lòng nhập giờ kết thúc cho tất cả ca làm việc');\n          return false;\n        }\n\n        if (!workShift.numberOfWorkers || workShift.numberOfWorkers <= 0) {\n          setError('Vui lòng nhập số lượng người lao động hợp lệ cho tất cả ca làm việc');\n          return false;\n        }\n\n        if (workShift.salary === undefined || workShift.salary < 0) {\n          setError('Vui lòng nhập mức lương hợp lệ cho tất cả ca làm việc');\n          return false;\n        }\n\n        if (!workShift.workingDays) {\n          setError('Vui lòng chọn ngày làm việc cho tất cả ca làm việc');\n          return false;\n        }\n      }\n    }\n\n    return true;\n  };\n\n  const handleSubmit = async () => {\n    // Prevent double submission with multiple checks\n    if (loading) {\n      console.log('Contract submission blocked: already loading');\n      return;\n    }\n\n    // Enhanced duplicate prevention\n    const now = Date.now();\n    const lastSubmission = localStorage.getItem('lastContractSubmission');\n    const submissionKey = `contract_${contract.customerId}_${contract.startingDate}_${contract.endingDate}_${Math.round(contract.totalAmount || 0)}`;\n    const lastSubmissionKey = localStorage.getItem('lastContractSubmissionKey');\n\n    // Prevent rapid successive submissions\n    if (lastSubmission && (now - parseInt(lastSubmission)) < 2000) {\n      console.log('Contract submission blocked: too rapid (within 2 seconds)');\n      setError('Vui lòng đợi ít nhất 2 giây trước khi gửi lại');\n      return;\n    }\n\n    // Prevent duplicate contract submissions\n    if (lastSubmissionKey === submissionKey && lastSubmission && (now - parseInt(lastSubmission)) < 60000) {\n      console.log('Contract submission blocked: duplicate contract detected');\n      setError('Hợp đồng tương tự đã được gửi gần đây. Vui lòng kiểm tra lại.');\n      return;\n    }\n\n    setError(null);\n\n    if (!validateContract()) {\n      return;\n    }\n\n    // Ensure total amount is calculated before submitting\n    const calculation = calculateContractAmount(contract);\n    const contractToSubmit = {\n      ...contract,\n      totalAmount: calculation.totalAmount\n    };\n\n    // Mark submission time and key to prevent rapid resubmission and duplicates\n    localStorage.setItem('lastContractSubmission', now.toString());\n    localStorage.setItem('lastContractSubmissionKey', submissionKey);\n    setLoading(true);\n\n    try {\n      console.log('🚀 Submitting contract creation request...', contractToSubmit);\n\n      // Clear any previous error state\n      setError(null);\n\n      const createdContract = await contractService.createContract(contractToSubmit as CustomerContract);\n      console.log('✅ Contract created successfully:', {\n        id: createdContract.id,\n        totalAmount: createdContract.totalAmount,\n        customerId: createdContract.customerId\n      });\n\n      // Verify the contract was actually created with valid data\n      if (!createdContract || !createdContract.id) {\n        throw new Error('Hợp đồng được tạo nhưng không nhận được thông tin hợp lệ từ máy chủ');\n      }\n\n      // Store the created contract for display\n      setCreatedContract(createdContract);\n      setSuccess(`Hợp đồng #${createdContract.id} đã được tạo thành công!`);\n\n      // Clear the submission timestamp and key on success\n      localStorage.removeItem('lastContractSubmission');\n      localStorage.removeItem('lastContractSubmissionKey');\n\n      // Set flag to trigger refresh in contracts list\n      localStorage.setItem('contractsListNeedsRefresh', 'true');\n    } catch (err: any) {\n      console.error('❌ Contract creation failed:', err);\n\n      // Provide more specific error messages\n      let errorMessage = 'Đã xảy ra lỗi khi tạo hợp đồng';\n\n      if (err.response?.status === 400) {\n        errorMessage = 'Dữ liệu hợp đồng không hợp lệ. Vui lòng kiểm tra lại thông tin.';\n      } else if (err.response?.status === 500) {\n        errorMessage = 'Lỗi máy chủ nội bộ. Vui lòng thử lại sau.';\n      } else if (err.message) {\n        errorMessage = err.message;\n      }\n\n      setError(errorMessage);\n\n      // Clear the submission timestamp on error to allow retry\n      localStorage.removeItem('lastContractSubmission');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return <LoadingSpinner fullScreen />;\n  }\n\n  return (\n    <Box>\n      {error && <ErrorAlert message={error} />}\n\n      {/* Show success alert with actions if contract was created */}\n      {createdContract && (\n        <ContractSuccessAlert\n          contract={createdContract}\n          onViewDetails={handleViewDetails}\n          onViewList={handleViewList}\n          onCreateNew={handleCreateNew}\n          redirectCountdown={redirectCountdown}\n        />\n      )}\n\n      {/* Show simple success message if no created contract yet */}\n      {success && !createdContract && <SuccessAlert message={success} />}\n\n      <CustomerContractForm\n        contract={contract}\n        onChange={handleContractChange}\n        onSubmit={handleSubmit}\n        loading={loading}\n      />\n    </Box>\n  );\n};\n\nexport default CreateContractPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,GAAG,QAAQ,eAAe;AACnC,SAASC,oBAAoB,QAAQ,wBAAwB;AAE7D,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,cAAc,EAAEC,UAAU,EAAEC,YAAY,EAAEC,oBAAoB,QAAQ,sBAAsB;AACrG,SAASC,uBAAuB,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5E,MAAMC,kBAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAA4B;IAClEkB,UAAU,EAAE,CAAC;IACbC,YAAY,EAAE,EAAE;IAChBC,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,EAAE;IACdC,MAAM,EAAE,CAAC,CAAC;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAgB,IAAI,CAAC;EAC3D,MAAM,CAAC+B,eAAe,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CAA0B,IAAI,CAAC;EACrF,MAAM,CAACiC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlC,QAAQ,CAAgB,IAAI,CAAC;;EAE/E;EACAC,SAAS,CAAC,MAAM;IACd,IAAI8B,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEI,EAAE,IAAIF,iBAAiB,KAAK,IAAI,EAAE;MACrD;MACAC,oBAAoB,CAAC,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAACH,eAAe,EAAEE,iBAAiB,CAAC,CAAC;;EAExC;EACAhC,SAAS,CAAC,MAAM;IACd,IAAIgC,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,GAAG,CAAC,EAAE;MACvD,MAAMG,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BH,oBAAoB,CAACD,iBAAiB,GAAG,CAAC,CAAC;MAC7C,CAAC,EAAE,IAAI,CAAC;MACR,OAAO,MAAMK,YAAY,CAACF,KAAK,CAAC;IAClC,CAAC,MAAM,IAAIH,iBAAiB,KAAK,CAAC,IAAIF,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEI,EAAE,EAAE;MACzD;MACApB,QAAQ,CAAC,cAAcgB,eAAe,CAACI,EAAE,EAAE,CAAC;IAC9C;EACF,CAAC,EAAE,CAACF,iBAAiB,EAAEF,eAAe,EAAEhB,QAAQ,CAAC,CAAC;EAElD,MAAMwB,oBAAoB,GAAIC,eAA0C,IAAK;IAC3EvB,WAAW,CAACuB,eAAe,CAAC;EAC9B,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAIV,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEI,EAAE,EAAE;MACvBpB,QAAQ,CAAC,cAAcgB,eAAe,CAACI,EAAE,EAAE,CAAC;IAC9C;EACF,CAAC;EAED,MAAMO,cAAc,GAAGA,CAAA,KAAM;IAC3B3B,QAAQ,CAAC,YAAY,CAAC;EACxB,CAAC;EAED,MAAM4B,eAAe,GAAGA,CAAA,KAAM;IAC5B;IACA1B,WAAW,CAAC;MACVC,UAAU,EAAE,CAAC;MACbC,YAAY,EAAE,EAAE;MAChBC,UAAU,EAAE,EAAE;MACdC,WAAW,EAAE,CAAC;MACdC,WAAW,EAAE,EAAE;MACfC,UAAU,EAAE,EAAE;MACdC,MAAM,EAAE;IACV,CAAC,CAAC;IACFQ,kBAAkB,CAAC,IAAI,CAAC;IACxBF,UAAU,CAAC,IAAI,CAAC;IAChBF,QAAQ,CAAC,IAAI,CAAC;IACdM,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMU,gBAAgB,GAAGA,CAAA,KAAe;IACtC,IAAI,CAAC5B,QAAQ,CAACE,UAAU,IAAIF,QAAQ,CAACE,UAAU,KAAK,CAAC,EAAE;MACrDU,QAAQ,CAAC,0BAA0B,CAAC;MACpC,OAAO,KAAK;IACd;;IAEA;IACA;IACA;;IAEA,IAAI,CAACZ,QAAQ,CAACO,UAAU,IAAIP,QAAQ,CAACO,UAAU,CAACsB,MAAM,KAAK,CAAC,EAAE;MAC5DjB,QAAQ,CAAC,8CAA8C,CAAC;MACxD,OAAO,KAAK;IACd;;IAEA;IACA,KAAK,MAAMkB,SAAS,IAAI9B,QAAQ,CAACO,UAAU,EAAE;MAC3C,IAAI,CAACuB,SAAS,CAACC,aAAa,IAAID,SAAS,CAACC,aAAa,KAAK,CAAC,EAAE;QAC7DnB,QAAQ,CAAC,4DAA4D,CAAC;QACtE,OAAO,KAAK;MACd;MAEA,IAAI,CAACkB,SAAS,CAACE,SAAS,EAAE;QACxBpB,QAAQ,CAAC,0DAA0D,CAAC;QACpE,OAAO,KAAK;MACd;MAEA,IAAI,CAACkB,SAAS,CAACG,OAAO,EAAE;QACtBrB,QAAQ,CAAC,2DAA2D,CAAC;QACrE,OAAO,KAAK;MACd;;MAEA;MACA;MACA;MACA;MACA;;MAEA,IAAI,CAACkB,SAAS,CAACI,UAAU,IAAIJ,SAAS,CAACI,UAAU,CAACL,MAAM,KAAK,CAAC,EAAE;QAC9DjB,QAAQ,CAAC,kEAAkE,CAAC;QAC5E,OAAO,KAAK;MACd;;MAEA;MACA,KAAK,MAAMuB,SAAS,IAAIL,SAAS,CAACI,UAAU,EAAE;QAC5C,IAAI,CAACC,SAAS,CAACC,SAAS,EAAE;UACxBxB,QAAQ,CAAC,kDAAkD,CAAC;UAC5D,OAAO,KAAK;QACd;QAEA,IAAI,CAACuB,SAAS,CAACE,OAAO,EAAE;UACtBzB,QAAQ,CAAC,mDAAmD,CAAC;UAC7D,OAAO,KAAK;QACd;QAEA,IAAI,CAACuB,SAAS,CAACG,eAAe,IAAIH,SAAS,CAACG,eAAe,IAAI,CAAC,EAAE;UAChE1B,QAAQ,CAAC,qEAAqE,CAAC;UAC/E,OAAO,KAAK;QACd;QAEA,IAAIuB,SAAS,CAACI,MAAM,KAAKC,SAAS,IAAIL,SAAS,CAACI,MAAM,GAAG,CAAC,EAAE;UAC1D3B,QAAQ,CAAC,uDAAuD,CAAC;UACjE,OAAO,KAAK;QACd;QAEA,IAAI,CAACuB,SAAS,CAACM,WAAW,EAAE;UAC1B7B,QAAQ,CAAC,oDAAoD,CAAC;UAC9D,OAAO,KAAK;QACd;MACF;IACF;IAEA,OAAO,IAAI;EACb,CAAC;EAED,MAAM8B,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B;IACA,IAAIjC,OAAO,EAAE;MACXkC,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;MAC3D;IACF;;IAEA;IACA,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;IACtB,MAAME,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC;IACrE,MAAMC,aAAa,GAAG,YAAYlD,QAAQ,CAACE,UAAU,IAAIF,QAAQ,CAACG,YAAY,IAAIH,QAAQ,CAACI,UAAU,IAAI+C,IAAI,CAACC,KAAK,CAACpD,QAAQ,CAACK,WAAW,IAAI,CAAC,CAAC,EAAE;IAChJ,MAAMgD,iBAAiB,GAAGL,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC;;IAE3E;IACA,IAAIF,cAAc,IAAKF,GAAG,GAAGS,QAAQ,CAACP,cAAc,CAAC,GAAI,IAAI,EAAE;MAC7DJ,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;MACxEhC,QAAQ,CAAC,+CAA+C,CAAC;MACzD;IACF;;IAEA;IACA,IAAIyC,iBAAiB,KAAKH,aAAa,IAAIH,cAAc,IAAKF,GAAG,GAAGS,QAAQ,CAACP,cAAc,CAAC,GAAI,KAAK,EAAE;MACrGJ,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;MACvEhC,QAAQ,CAAC,+DAA+D,CAAC;MACzE;IACF;IAEAA,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI,CAACgB,gBAAgB,CAAC,CAAC,EAAE;MACvB;IACF;;IAEA;IACA,MAAM2B,WAAW,GAAG7D,uBAAuB,CAACM,QAAQ,CAAC;IACrD,MAAMwD,gBAAgB,GAAG;MACvB,GAAGxD,QAAQ;MACXK,WAAW,EAAEkD,WAAW,CAAClD;IAC3B,CAAC;;IAED;IACA2C,YAAY,CAACS,OAAO,CAAC,wBAAwB,EAAEZ,GAAG,CAACa,QAAQ,CAAC,CAAC,CAAC;IAC9DV,YAAY,CAACS,OAAO,CAAC,2BAA2B,EAAEP,aAAa,CAAC;IAChExC,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACFiC,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEY,gBAAgB,CAAC;;MAE3E;MACA5C,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMG,eAAe,GAAG,MAAM1B,eAAe,CAACsE,cAAc,CAACH,gBAAoC,CAAC;MAClGb,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;QAC9CzB,EAAE,EAAEJ,eAAe,CAACI,EAAE;QACtBd,WAAW,EAAEU,eAAe,CAACV,WAAW;QACxCH,UAAU,EAAEa,eAAe,CAACb;MAC9B,CAAC,CAAC;;MAEF;MACA,IAAI,CAACa,eAAe,IAAI,CAACA,eAAe,CAACI,EAAE,EAAE;QAC3C,MAAM,IAAIyC,KAAK,CAAC,qEAAqE,CAAC;MACxF;;MAEA;MACA5C,kBAAkB,CAACD,eAAe,CAAC;MACnCD,UAAU,CAAC,aAAaC,eAAe,CAACI,EAAE,0BAA0B,CAAC;;MAErE;MACA6B,YAAY,CAACa,UAAU,CAAC,wBAAwB,CAAC;MACjDb,YAAY,CAACa,UAAU,CAAC,2BAA2B,CAAC;;MAEpD;MACAb,YAAY,CAACS,OAAO,CAAC,2BAA2B,EAAE,MAAM,CAAC;IAC3D,CAAC,CAAC,OAAOK,GAAQ,EAAE;MAAA,IAAAC,aAAA,EAAAC,cAAA;MACjBrB,OAAO,CAAChC,KAAK,CAAC,6BAA6B,EAAEmD,GAAG,CAAC;;MAEjD;MACA,IAAIG,YAAY,GAAG,gCAAgC;MAEnD,IAAI,EAAAF,aAAA,GAAAD,GAAG,CAACI,QAAQ,cAAAH,aAAA,uBAAZA,aAAA,CAAcvD,MAAM,MAAK,GAAG,EAAE;QAChCyD,YAAY,GAAG,iEAAiE;MAClF,CAAC,MAAM,IAAI,EAAAD,cAAA,GAAAF,GAAG,CAACI,QAAQ,cAAAF,cAAA,uBAAZA,cAAA,CAAcxD,MAAM,MAAK,GAAG,EAAE;QACvCyD,YAAY,GAAG,2CAA2C;MAC5D,CAAC,MAAM,IAAIH,GAAG,CAACK,OAAO,EAAE;QACtBF,YAAY,GAAGH,GAAG,CAACK,OAAO;MAC5B;MAEAvD,QAAQ,CAACqD,YAAY,CAAC;;MAEtB;MACAjB,YAAY,CAACa,UAAU,CAAC,wBAAwB,CAAC;IACnD,CAAC,SAAS;MACRnD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,EAAE;IACX,oBAAOb,OAAA,CAACN,cAAc;MAAC8E,UAAU;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACtC;EAEA,oBACE5E,OAAA,CAACT,GAAG;IAAAsF,QAAA,GACD9D,KAAK,iBAAIf,OAAA,CAACL,UAAU;MAAC4E,OAAO,EAAExD;IAAM;MAAA0D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGvCzD,eAAe,iBACdnB,OAAA,CAACH,oBAAoB;MACnBO,QAAQ,EAAEe,eAAgB;MAC1B2D,aAAa,EAAEjD,iBAAkB;MACjCkD,UAAU,EAAEjD,cAAe;MAC3BkD,WAAW,EAAEjD,eAAgB;MAC7BV,iBAAiB,EAAEA;IAAkB;MAAAoD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CACF,EAGA3D,OAAO,IAAI,CAACE,eAAe,iBAAInB,OAAA,CAACJ,YAAY;MAAC2E,OAAO,EAAEtD;IAAQ;MAAAwD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAElE5E,OAAA,CAACR,oBAAoB;MACnBY,QAAQ,EAAEA,QAAS;MACnB6E,QAAQ,EAAEtD,oBAAqB;MAC/BuD,QAAQ,EAAEpC,YAAa;MACvBjC,OAAO,EAAEA;IAAQ;MAAA4D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC1E,EAAA,CA9QID,kBAA4B;EAAA,QACfX,WAAW;AAAA;AAAA6F,EAAA,GADxBlF,kBAA4B;AAgRlC,eAAeA,kBAAkB;AAAC,IAAAkF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}