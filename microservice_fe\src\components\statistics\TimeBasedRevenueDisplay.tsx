import React, { useState } from 'react';
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Box,
  ToggleButton,
  ToggleButtonGroup,
  TableSortLabel,
  TablePagination
} from '@mui/material';
import { TimeBasedRevenue } from '../../models';
import BarChartDisplay from './BarChartDisplay';
import StatisticsSummary from './StatisticsSummary';
import BarChartIcon from '@mui/icons-material/BarChart';
import TableChartIcon from '@mui/icons-material/TableChart';

interface TimeBasedRevenueDisplayProps {
  data: TimeBasedRevenue[];
  periodType: string;
  periodLabel?: string;
}

type SortOrder = 'asc' | 'desc';
type SortField = 'label' | 'invoiceCount' | 'totalRevenue';

const TimeBasedRevenueDisplay: React.FC<TimeBasedRevenueDisplayProps> = ({
  data,
  periodType,
  periodLabel
}) => {
  // State for view mode (chart or table)
  const [viewMode, setViewMode] = useState<'chart' | 'table'>('chart');

  // State for sorting
  const [sortField, setSortField] = useState<SortField>('label');
  const [sortOrder, setSortOrder] = useState<SortOrder>('asc');

  // State for pagination
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  // Format currency to VND
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(amount);
  };

  // Get period type label
  const getPeriodTypeLabel = (): string => {
    switch (periodType) {
      case 'daily':
        return 'Ngày';
      case 'monthly':
        return 'Tháng';
      case 'quarterly':
        return 'Quý';
      case 'yearly':
        return 'Năm';
      default:
        return 'Thời gian';
    }
  };

  // Handle view mode change
  const handleViewModeChange = (
    event: React.MouseEvent<HTMLElement>,
    newViewMode: 'chart' | 'table',
  ) => {
    if (newViewMode !== null) {
      setViewMode(newViewMode);
    }
  };

  // Handle sort request
  const handleRequestSort = (field: SortField) => {
    const isAsc = sortField === field && sortOrder === 'asc';
    setSortOrder(isAsc ? 'desc' : 'asc');
    setSortField(field);
  };

  // Handle page change
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Sort data
  const sortedData = [...data].sort((a, b) => {
    const compareValue = (fieldA: any, fieldB: any) => {
      if (fieldA < fieldB) return sortOrder === 'asc' ? -1 : 1;
      if (fieldA > fieldB) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    };

    return compareValue(a[sortField], b[sortField]);
  });

  // Paginate data
  const paginatedData = sortedData.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  return (
    <Box>
      {/* Statistics Summary */}
      <StatisticsSummary
        data={data}
        periodType={periodType}
        periodLabel={periodLabel}
      />

      {/* View Mode Toggle */}
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
        <ToggleButtonGroup
          value={viewMode}
          exclusive
          onChange={handleViewModeChange}
          aria-label="Chế độ xem"
          size="small"
        >
          <ToggleButton value="chart" aria-label="Biểu đồ">
            <BarChartIcon />
          </ToggleButton>
          <ToggleButton value="table" aria-label="Bảng">
            <TableChartIcon />
          </ToggleButton>
        </ToggleButtonGroup>
      </Box>

      {/* Chart View */}
      {viewMode === 'chart' && (
        <BarChartDisplay
          data={data}
          periodType={periodType}
          title={`Doanh thu theo ${getPeriodTypeLabel().toLowerCase()}${periodLabel ? ` ${periodLabel}` : ''}`}
        />
      )}

      {/* Table View */}
      {viewMode === 'table' && (
        <Paper elevation={2} sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Doanh thu theo {getPeriodTypeLabel().toLowerCase()}{periodLabel ? ` ${periodLabel}` : ''}
          </Typography>

          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>
                    <TableSortLabel
                      active={sortField === 'label'}
                      direction={sortField === 'label' ? sortOrder : 'asc'}
                      onClick={() => handleRequestSort('label')}
                    >
                      {getPeriodTypeLabel()}
                    </TableSortLabel>
                  </TableCell>
                  <TableCell align="right">
                    <TableSortLabel
                      active={sortField === 'totalRevenue'}
                      direction={sortField === 'totalRevenue' ? sortOrder : 'asc'}
                      onClick={() => handleRequestSort('totalRevenue')}
                    >
                      Doanh thu
                    </TableSortLabel>
                  </TableCell>
                  <TableCell align="center">
                    <TableSortLabel
                      active={sortField === 'invoiceCount'}
                      direction={sortField === 'invoiceCount' ? sortOrder : 'asc'}
                      onClick={() => handleRequestSort('invoiceCount')}
                    >
                      Số lượng hóa đơn
                    </TableSortLabel>
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedData.length > 0 ? (
                  paginatedData.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell>{item.label}</TableCell>
                      <TableCell align="right">{formatCurrency(item.totalRevenue)}</TableCell>
                      <TableCell align="center">{item.invoiceCount}</TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={3} align="center">
                      <Box sx={{ py: 3 }}>
                        <Typography variant="subtitle1">
                          Không có dữ liệu doanh thu trong khoảng thời gian đã chọn
                        </Typography>
                      </Box>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>

          {data.length > 0 && (
            <TablePagination
              rowsPerPageOptions={[5, 10, 25]}
              component="div"
              count={data.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
              labelRowsPerPage="Số dòng mỗi trang:"
              labelDisplayedRows={({ from, to, count }) => `${from}-${to} của ${count}`}
            />
          )}
        </Paper>
      )}
    </Box>
  );
};

export default TimeBasedRevenueDisplay;
