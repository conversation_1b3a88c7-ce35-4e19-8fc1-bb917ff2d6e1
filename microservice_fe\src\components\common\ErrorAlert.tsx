import React, { useState, useEffect } from 'react';
import { Alert, AlertTitle, Box, IconButton, Collapse } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import RefreshIcon from '@mui/icons-material/Refresh';

interface ErrorAlertProps {
  title?: string;
  message: string;
  onRetry?: () => void;
  autoHide?: boolean;
  hideAfter?: number; // in milliseconds
}

function ErrorAlert({
  title = 'Lỗi',
  message,
  onRetry,
  autoHide = false,
  hideAfter = 10000 // 10 seconds default
}: ErrorAlertProps) {
  const [open, setOpen] = useState(true);

  // Auto-hide functionality
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (autoHide && open) {
      timer = setTimeout(() => {
        setOpen(false);
      }, hideAfter);
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [autoHide, hideAfter, open]);

  // Format error message for better display
  const formatErrorMessage = (msg: string) => {
    // If message is too long, truncate it
    if (msg.length > 200) {
      return msg.substring(0, 200) + '...';
    }
    return msg;
  };

  return (
    <Box sx={{ my: 2 }}>
      <Collapse in={open}>
        <Alert
          severity="error"
          action={
            <Box>
              {onRetry && (
                <IconButton
                  aria-label="retry"
                  color="inherit"
                  size="small"
                  onClick={onRetry}
                  title="Thử lại"
                >
                  <RefreshIcon fontSize="inherit" />
                </IconButton>
              )}
              <IconButton
                aria-label="close"
                color="inherit"
                size="small"
                onClick={() => setOpen(false)}
              >
                <CloseIcon fontSize="inherit" />
              </IconButton>
            </Box>
          }
          sx={{
            '& .MuiAlert-message': {
              wordBreak: 'break-word',
              whiteSpace: 'pre-wrap'
            }
          }}
        >
          <AlertTitle>{title}</AlertTitle>
          {formatErrorMessage(message)}
        </Alert>
      </Collapse>
    </Box>
  );
};

export default ErrorAlert;
