import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Box,
  Typography,
  Button,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import PrintIcon from '@mui/icons-material/Print';
import ReceiptIcon from '@mui/icons-material/Receipt';
import { CustomerPayment, CustomerContract, PaymentMethodMap } from '../../models';
import { formatCurrency } from '../../utils/formatters';
import { formatDateLocalized } from '../../utils/dateUtils';

interface PaymentInvoiceProps {
  open: boolean;
  payment: CustomerPayment | null;
  contracts: CustomerContract[];
  customerName: string;
  onClose: () => void;
}

const PaymentInvoice: React.FC<PaymentInvoiceProps> = ({
  open,
  payment,
  contracts,
  customerName,
  onClose,
}) => {
  const handlePrint = () => {
    window.print();
  };

  if (!payment) return null;

  const paymentDate = formatDateLocalized(payment.paymentDate);
  const paymentMethodText = PaymentMethodMap[payment.paymentMethod] || 'Không xác định';

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          minHeight: '70vh',
        },
      }}
    >
      <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <ReceiptIcon color="primary" />
          <Typography variant="h6">
            Hóa đơn thanh toán #{payment.id}
          </Typography>
        </Box>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent>
        <Box sx={{ p: 2 }}>
          {/* Header thông tin công ty */}
          <Box sx={{ textAlign: 'center', mb: 4 }}>
            <Typography variant="h4" fontWeight="bold" color="primary">
              CÔNG TY QUẢN LÝ NHÂN CÔNG
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Địa chỉ: 123 Đường ABC, Quận XYZ, TP.HCM
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Điện thoại: (028) 1234-5678 | Email: <EMAIL>
            </Typography>
          </Box>

          <Divider sx={{ mb: 3 }} />

          {/* Thông tin hóa đơn */}
          <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 3, mb: 3 }}>
            <Box>
              <Typography variant="h6" gutterBottom color="primary">
                Thông tin khách hàng
              </Typography>
              <Typography variant="body1">
                <strong>Tên khách hàng:</strong> {customerName}
              </Typography>
            </Box>
            <Box>
              <Typography variant="h6" gutterBottom color="primary">
                Thông tin thanh toán
              </Typography>
              <Typography variant="body1">
                <strong>Mã thanh toán:</strong> #{payment.id}
              </Typography>
              <Typography variant="body1">
                <strong>Ngày thanh toán:</strong> {paymentDate}
              </Typography>
              <Typography variant="body1">
                <strong>Phương thức:</strong> {paymentMethodText}
              </Typography>
            </Box>
          </Box>

          <Divider sx={{ mb: 3 }} />

          {/* Chi tiết hợp đồng được thanh toán */}
          <Typography variant="h6" gutterBottom color="primary">
            Chi tiết hợp đồng được thanh toán
          </Typography>
          
          <TableContainer component={Paper} variant="outlined" sx={{ mb: 3 }}>
            <Table size="small">
              <TableHead>
                <TableRow sx={{ bgcolor: 'primary.light' }}>
                  <TableCell>Mã hợp đồng</TableCell>
                  <TableCell>Ngày bắt đầu</TableCell>
                  <TableCell>Ngày kết thúc</TableCell>
                  <TableCell align="right">Tổng giá trị</TableCell>
                  <TableCell align="right">Số tiền thanh toán</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {contracts.map((contract) => {
                  // Tìm số tiền thanh toán cho hợp đồng này
                  const contractPayment = payment.contractPayments?.find(
                    cp => cp.contractId === contract.id
                  );
                  const paidAmount = contractPayment?.allocatedAmount || payment.paymentAmount;

                  return (
                    <TableRow key={contract.id}>
                      <TableCell>#{contract.id}</TableCell>
                      <TableCell>{formatDateLocalized(contract.startingDate)}</TableCell>
                      <TableCell>{formatDateLocalized(contract.endingDate)}</TableCell>
                      <TableCell align="right">{formatCurrency(contract.totalAmount)}</TableCell>
                      <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                        {formatCurrency(paidAmount)}
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Tổng kết */}
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 3 }}>
            <Box sx={{ minWidth: 300 }}>
              <Divider sx={{ mb: 2 }} />
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="h6">
                  Tổng số tiền thanh toán:
                </Typography>
                <Typography variant="h6" color="primary" fontWeight="bold">
                  {formatCurrency(payment.paymentAmount)}
                </Typography>
              </Box>
              <Divider />
            </Box>
          </Box>

          {/* Ghi chú */}
          {payment.note && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="h6" gutterBottom color="primary">
                Ghi chú
              </Typography>
              <Typography variant="body1" sx={{ fontStyle: 'italic' }}>
                {payment.note}
              </Typography>
            </Box>
          )}

          {/* Chữ ký */}
          <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 4, mt: 4 }}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="body1" fontWeight="bold">
                Người thanh toán
              </Typography>
              <Typography variant="body2" color="text.secondary">
                (Ký và ghi rõ họ tên)
              </Typography>
              <Box sx={{ height: 80 }} />
              <Typography variant="body1">
                {customerName}
              </Typography>
            </Box>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="body1" fontWeight="bold">
                Người thu tiền
              </Typography>
              <Typography variant="body2" color="text.secondary">
                (Ký và ghi rõ họ tên)
              </Typography>
              <Box sx={{ height: 80 }} />
              <Typography variant="body1">
                _________________
              </Typography>
            </Box>
          </Box>

          {/* Footer */}
          <Box sx={{ textAlign: 'center', mt: 4, pt: 2, borderTop: '1px solid #ddd' }}>
            <Typography variant="body2" color="text.secondary">
              Hóa đơn được tạo tự động bởi hệ thống quản lý nhân công
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Ngày in: {formatDateLocalized(new Date().toISOString())}
            </Typography>
          </Box>
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 2, gap: 1 }}>
        <Button
          variant="outlined"
          startIcon={<PrintIcon />}
          onClick={handlePrint}
        >
          In hóa đơn
        </Button>
        <Button
          variant="contained"
          onClick={onClose}
        >
          Đóng
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PaymentInvoice;
