
import {
  Box,
  Typography,
  Card,
  CardContent,
  useTheme,
  Divider,
} from '@mui/material';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import EventIcon from '@mui/icons-material/Event';
import { WorkShift, JobDetail } from '../../models';
import { calculateWorkingDates, formatWorkingDays } from '../../utils/workingDaysUtils';
import { formatCurrency } from '../../utils/formatters';

interface WorkingDatesPreviewProps {
  workShift: WorkShift;
  jobDetail: JobDetail;
  shiftIndex: number;
}

function WorkingDatesPreview({
  workShift,
  jobDetail,
  shiftIndex
}: WorkingDatesPreviewProps) {
  const theme = useTheme();

  // Debug log to verify component is rendering
  console.log('WorkingDatesPreview rendering with updated code', { workShift, jobDetail });

  // Calculate working dates for this shift
  const workingDates = calculateWorkingDates(
    jobDetail.startDate,
    jobDetail.endDate,
    workShift.workingDays
  );

  // Helper function to get Vietnamese day name
  const getDayOfWeek = (dateStr: string) => {
    const [d, m, y] = dateStr.split('/').map(Number);
    const date = new Date(y, m - 1, d);
    const day = date.getDay();
    const dayNames = ['Chủ nhật', 'Thứ hai', 'Thứ ba', 'Thứ tư', 'Thứ năm', 'Thứ sáu', 'Thứ bảy'];
    return dayNames[day];
  };

  // Generate detailed work schedule items
  const workScheduleItems = workingDates.map(date => ({
    date,
    startTime: workShift.startTime,
    endTime: workShift.endTime
  }));

  // Sort by date
  workScheduleItems.sort((a, b) => {
    const [d1, m1, y1] = a.date.split('/').map(Number);
    const [d2, m2, y2] = b.date.split('/').map(Number);
    return new Date(y1, m1 - 1, d1).getTime() - new Date(y2, m2 - 1, d2).getTime();
  });

  // Calculate total amount for this shift
  const totalAmount = workShift.salary && workShift.numberOfWorkers && workingDates.length
    ? workShift.salary * workShift.numberOfWorkers * workingDates.length
    : 0;

  if (!workShift.workingDays || !jobDetail.startDate || !jobDetail.endDate) {
    return (
      <Card variant="outlined" sx={{ mb: 2, borderColor: theme.palette.warning.light }}>
        <CardContent>
          <Typography variant="body2" color="text.secondary">
            Vui lòng chọn ngày làm việc và thời gian để xem lịch làm việc chi tiết
          </Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card variant="outlined" sx={{ mb: 2, borderColor: theme.palette.primary.light }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <CalendarMonthIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
          <Typography variant="subtitle1" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
            Ca làm việc ({workShift.startTime} - {workShift.endTime})
          </Typography>
        </Box>

        {/* Summary Information */}
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 2 }}>
          <Box sx={{ minWidth: '150px', flex: 1 }}>
            <Typography variant="body2" color="text.secondary">Ngày làm việc</Typography>
            <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
              {formatWorkingDays(workShift.workingDays)}
            </Typography>
          </Box>
          <Box sx={{ minWidth: '120px' }}>
            <Typography variant="body2" color="text.secondary">Tổng số ngày</Typography>
            <Typography variant="body1" sx={{ fontWeight: 'medium', color: theme.palette.primary.main }}>
              {workingDates.length} ngày
            </Typography>
          </Box>
          <Box sx={{ minWidth: '120px' }}>
            <Typography variant="body2" color="text.secondary">Số nhân công</Typography>
            <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
              {workShift.numberOfWorkers} người
            </Typography>
          </Box>
          <Box sx={{ minWidth: '150px' }}>
            <Typography variant="body2" color="text.secondary">Tổng tiền ca</Typography>
            <Typography variant="body1" sx={{ fontWeight: 'bold', color: theme.palette.success.main }}>
              {formatCurrency(totalAmount)}
            </Typography>
          </Box>
        </Box>

        <Divider sx={{ mb: 2 }} />

        {/* Detailed working schedule */}
        <Box>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <EventIcon fontSize="small" sx={{ mr: 1 }} />
            <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
              Lịch làm việc chi tiết ({workingDates.length} ngày):
            </Typography>
          </Box>

          {workScheduleItems.length === 0 ? (
            <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
              Không có ngày làm việc nào trong khoảng thời gian đã chọn
            </Typography>
          ) : (
            <Box sx={{ pl: 3, mb: 0, maxHeight: '200px', overflowY: 'auto' }}>
              {workScheduleItems.map((item, idx) => (
                <Box key={idx} sx={{ mb: 0.75, display: 'flex', alignItems: 'center' }}>
                  <Box sx={{ width: 4, height: 4, borderRadius: '50%', backgroundColor: 'primary.main', mr: 1 }} />
                  <Typography variant="body2" sx={{ fontSize: '0.9rem' }}>
                    {getDayOfWeek(item.date)}, ngày {item.date} ca {item.startTime} - {item.endTime}
                  </Typography>
                </Box>
              ))}
            </Box>
          )}
        </Box>

        {/* Calculation breakdown */}
        {totalAmount > 0 && (
          <Box sx={{ mt: 2, p: 2, backgroundColor: theme.palette.success.light, borderRadius: '4px' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 1 }}>
              Tính toán chi tiết:
            </Typography>
            <Typography variant="body2">
              {formatCurrency(workShift.salary || 0)} × {workShift.numberOfWorkers} người × {workingDates.length} ngày = {formatCurrency(totalAmount)}
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default WorkingDatesPreview;
