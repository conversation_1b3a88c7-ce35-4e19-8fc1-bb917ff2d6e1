const webpack = require('webpack');

module.exports = {
  webpack: {
    configure: (webpackConfig) => {
      // For webpack 4 compatibility, use node configuration instead of fallback
      webpackConfig.node = {
        ...webpackConfig.node,
        fs: 'empty',
        net: 'empty',
        tls: 'empty',
        child_process: 'empty',
      };

      // Add plugins to provide global variables and polyfills
      webpackConfig.plugins = [
        ...webpackConfig.plugins,
        new webpack.ProvidePlugin({
          process: 'process/browser.js',
          Buffer: ['buffer', 'Buffer'],
        }),
      ];

      return webpackConfig;
    },
  },
};
