import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Typography,
  TextField,
  Card,
  CardContent,
  CardActions,
  Divider,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import SearchIcon from '@mui/icons-material/Search';
import VisibilityIcon from '@mui/icons-material/Visibility';
import EditIcon from '@mui/icons-material/Edit';
import { CustomerContract, ContractStatusMap } from '../models';
import { contractService } from '../services/contract/contractService';
import { LoadingSpinner, ErrorAlert, PageHeader } from '../components/common';
import { formatDateLocalized } from '../utils/dateUtils';
import { formatCurrency } from '../utils/currencyUtils';

const ContractsListPage: React.FC = () => {
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [contracts, setContracts] = useState<CustomerContract[]>([]);
  const [filteredContracts, setFilteredContracts] = useState<CustomerContract[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  const fetchContracts = async () => {
    setLoading(true);
    try {
      const data = await contractService.getAllContracts();
      // Sort contracts by creation date (newest first)
      const sortedData = [...data].sort((a, b) => {
        const dateA = new Date(a.createdAt || '');
        const dateB = new Date(b.createdAt || '');
        return dateB.getTime() - dateA.getTime();
      });
      setContracts(sortedData);
      setFilteredContracts(sortedData);
    } catch (err: any) {
      setError(err.message || 'Đã xảy ra lỗi khi tải danh sách hợp đồng');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchContracts();
  }, []);

  // Listen for refresh flag from localStorage
  useEffect(() => {
    const handleStorageChange = () => {
      const needsRefresh = localStorage.getItem('contractsListNeedsRefresh');
      if (needsRefresh === 'true') {
        localStorage.removeItem('contractsListNeedsRefresh');
        fetchContracts();
      }
    };

    // Check on component mount
    handleStorageChange();

    // Listen for storage changes
    window.addEventListener('storage', handleStorageChange);

    // Also listen for focus events (when user returns to tab)
    window.addEventListener('focus', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('focus', handleStorageChange);
    };
  }, []);

  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredContracts(contracts);
      return;
    }

    const lowercasedSearch = searchTerm.toLowerCase();
    const filtered = contracts.filter(
      (contract) =>
        String(contract.id).includes(lowercasedSearch) ||
        contract.customerName?.toLowerCase().includes(lowercasedSearch)
    );
    setFilteredContracts(filtered);
  }, [searchTerm, contracts]);

  const handleCreateContract = () => {
    navigate('/contracts/create');
  };

  const handleViewContract = (id: number) => {
    navigate(`/contracts/${id}`);
  };

  const handleEditContract = (id: number) => {
    navigate(`/contracts/edit/${id}`);
  };

  const getStatusColor = (status: number) => {
    switch (status) {
      case 0: // Pending
        return 'warning';
      case 1: // Active
        return 'success';
      case 2: // Completed
        return 'info';
      case 3: // Cancelled
        return 'error';
      default:
        return 'default';
    }
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return <ErrorAlert message={error} />;
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <PageHeader title="Hợp đồng" subtitle="Quản lý hợp đồng khách hàng" />
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleCreateContract}
        >
          Tạo hợp đồng
        </Button>
      </Box>

      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ position: 'relative' }}>
          <TextField
            fullWidth
            placeholder="Tìm kiếm hợp đồng theo ID, tên khách hàng hoặc địa chỉ"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            sx={{ pl: 4 }}
          />
          <Box sx={{ position: 'absolute', left: 10, top: '50%', transform: 'translateY(-50%)' }}>
            <SearchIcon color="action" />
          </Box>
        </Box>
      </Paper>

      {filteredContracts.length === 0 ? (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h6" color="text.secondary">
            Không tìm thấy hợp đồng nào
          </Typography>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleCreateContract}
            sx={{ mt: 2 }}
          >
            Tạo hợp đồng đầu tiên của bạn
          </Button>
        </Paper>
      ) : isMobile ? (
        // Mobile view - card list
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          {filteredContracts.map((contract) => (
            <Box sx={{ width: '100%' }} key={contract.id}>
              <Card variant="outlined">
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Typography variant="h6">#{contract.id}</Typography>
                    <Chip
                      label={ContractStatusMap[contract.status || 0]}
                      color={getStatusColor(contract.status || 0)}
                      size="small"
                    />
                  </Box>

                  <Typography variant="subtitle1" color="text.secondary" gutterBottom>
                    <strong>Khách hàng:</strong> {contract.customerName || `Khách hàng #${contract.customerId}`}
                  </Typography>

                  <Divider sx={{ my: 1 }} />

                  <Box sx={{ display: 'flex', flexWrap: 'wrap', mx: -1 }}>
                    <Box sx={{ width: '50%', p: 1 }}>
                      <Typography variant="body2" color="text.secondary">Ngày bắt đầu</Typography>
                      <Typography variant="body2">{formatDateLocalized(contract.startingDate)}</Typography>
                    </Box>
                    <Box sx={{ width: '50%', p: 1 }}>
                      <Typography variant="body2" color="text.secondary">Ngày kết thúc</Typography>
                      <Typography variant="body2">{formatDateLocalized(contract.endingDate)}</Typography>
                    </Box>
                    <Box sx={{ width: '50%', p: 1 }}>
                      <Typography variant="body2" color="text.secondary">Tổng tiền</Typography>
                      <Typography variant="body2" fontWeight="bold">{formatCurrency(contract.totalAmount)}</Typography>
                    </Box>
                    <Box sx={{ width: '50%', p: 1 }}>
                      <Typography variant="body2" color="text.secondary">Ngày tạo</Typography>
                      <Typography variant="body2">{formatDateLocalized(contract.createdAt || '')}</Typography>
                    </Box>
                  </Box>
                </CardContent>
                <CardActions>
                  <Button
                    size="small"
                    startIcon={<VisibilityIcon />}
                    onClick={() => handleViewContract(contract.id!)}
                  >
                    Xem
                  </Button>
                  <Button
                    size="small"
                    startIcon={<EditIcon />}
                    onClick={() => handleEditContract(contract.id!)}
                  >
                    Sửa
                  </Button>
                </CardActions>
              </Card>
            </Box>
          ))}
        </Box>
      ) : (
        // Desktop view - table
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Mã hợp đồng</TableCell>
                <TableCell>Khách hàng</TableCell>
                <TableCell>Ngày bắt đầu</TableCell>
                <TableCell>Ngày kết thúc</TableCell>
                <TableCell>Tổng tiền</TableCell>
                <TableCell>Trạng thái</TableCell>
                <TableCell>Ngày tạo</TableCell>
                <TableCell>Thao tác</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredContracts.map((contract) => (
                <TableRow key={contract.id}>
                  <TableCell>#{contract.id}</TableCell>
                  <TableCell>{contract.customerName || `Khách hàng #${contract.customerId}`}</TableCell>
                  <TableCell>{formatDateLocalized(contract.startingDate)}</TableCell>
                  <TableCell>{formatDateLocalized(contract.endingDate)}</TableCell>
                  <TableCell>{formatCurrency(contract.totalAmount)}</TableCell>
                  <TableCell>
                    <Chip
                      label={ContractStatusMap[contract.status || 0]}
                      color={getStatusColor(contract.status || 0)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>{formatDateLocalized(contract.createdAt || '')}</TableCell>
                  <TableCell>
                    <IconButton
                      color="primary"
                      onClick={() => handleViewContract(contract.id!)}
                      title="Xem"
                    >
                      <VisibilityIcon />
                    </IconButton>
                    <IconButton
                      color="secondary"
                      onClick={() => handleEditContract(contract.id!)}
                      title="Sửa"
                    >
                      <EditIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Box>
  );
};

export default ContractsListPage;
